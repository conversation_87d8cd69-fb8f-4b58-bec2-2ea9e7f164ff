import Foundation
import Combine
import CoreLocation

// MARK: - 探索错误类型
enum ExploreError: LocalizedError {
    case noRoutesGenerated
    case noValidRoutes
    case invalidLocationData

    var errorDescription: String? {
        switch self {
        case .noRoutesGenerated:
            return "未能生成任何路线，请检查网络连接或重试"
        case .noValidRoutes:
            return "生成的路线数据不完整，请重新生成"
        case .invalidLocationData:
            return "位置信息无效，请重新选择起始位置"
        }
    }
}

@MainActor
class ExploreManager: ObservableObject {
    static let shared = ExploreManager()
    
    @Published var exploreActivities: [ExploreActivity] = []
    @Published var isLoading: Bool = false
    @Published var error: String?
    @Published var hasGeneratedForCurrentLocation: Bool = false
    
    private let locationManager = ExploreLocationManager.shared
    private let deepSeekManager = DeepSeekAPIManager.shared
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - 任务管理
    private var currentGenerationTask: Task<Void, Never>?
    private var isGenerating = false
    private var lastGenerationRequest: String = ""  // 用于防止重复请求
    private var lastGenerationTime: Date = Date.distantPast  // 记录上次生成时间
    
    private init() {
        setupBindings()
    }
    
    private func setupBindings() {
        // 监听位置变化
        locationManager.$currentCity
            .sink { [weak self] (city: String) in
                if !city.isEmpty && !(self?.hasGeneratedForCurrentLocation ?? false) {
                    Task {
                        await self?.generateRoutesForCurrentLocation()
                    }
                }
            }
            .store(in: &cancellables)
        
        // 监听DeepSeek API状态
        deepSeekManager.$isGenerating
            .assign(to: \.isLoading, on: self)
            .store(in: &cancellables)
        
        deepSeekManager.$error
            .compactMap { $0?.localizedDescription }
            .assign(to: \.error, on: self)
            .store(in: &cancellables)
    }
    
    func startExploring() {
        guard !isLoading else { return }
        
        // 首先获取位置
        if locationManager.currentCity.isEmpty {
            locationManager.requestLocationPermission()
        } else {
            Task {
                await generateRoutesForCurrentLocation()
            }
        }
    }
    
    func refreshRoutes() {
        Task {
            await generateRoutesForCurrentLocation(forceRefresh: true)
        }
    }
    
    private func generateRoutesForCurrentLocation(forceRefresh: Bool = false) async {
        let city = locationManager.currentCity
        
        guard !city.isEmpty else {
            error = "无法获取当前城市信息"
            return
        }
        
        if !forceRefresh && hasGeneratedForCurrentLocation {
            return
        }
        
        do {
            let routes = try await deepSeekManager.generateExploreRoutes(for: city, count: 3)
            
            await MainActor.run {
                self.exploreActivities = routes
                self.hasGeneratedForCurrentLocation = true
                self.error = nil
                
                print("🎯 成功生成\(routes.count)条\(city)的探索路线")
                
                // 打印路线信息用于调试
                for route in routes {
                    print("📍 路线: \(route.title)")
                    print("   距离: \(route.distance)km")
                    print("   轨迹点数: \(route.gpxTrack.count)")
                }
            }
            
        } catch {
            await MainActor.run {
                self.error = "生成探索路线失败: \(error.localizedDescription)"
                print("❌ 生成路线失败: \(error)")
            }
        }
    }
    
    func generateMoreRoutes() {
        let city = locationManager.currentCity
        
        guard !city.isEmpty else {
            error = "无法获取当前城市信息"
            return
        }
        
        Task {
            do {
                let newRoutes = try await deepSeekManager.generateExploreRoutes(for: city, count: 2)
                
                await MainActor.run {
                    self.exploreActivities.append(contentsOf: newRoutes)
                    self.error = nil
                    
                    print("➕ 新增\(newRoutes.count)条探索路线")
                }
                
            } catch {
                await MainActor.run {
                    self.error = "生成更多路线失败: \(error.localizedDescription)"
                }
            }
        }
    }
    
    func generateCustomRoutes(startLocation: String, requirements: String, minDistance: Double = 5.0, maxDistance: Double = 25.0) {
        // 生成请求标识符用于去重
        let requestSignature = "\(startLocation)-\(requirements)-\(Int(minDistance))-\(Int(maxDistance))"
        let currentTime = Date()
        
        // 防止重复生成：检查任务状态、请求相似性和时间间隔
        if isGenerating {
            print("⚠️ 已有路线生成任务在进行中，忽略重复请求")
            return
        }
        
        // 防止短时间内的重复请求（30秒内相同请求）
        if requestSignature == lastGenerationRequest && 
           currentTime.timeIntervalSince(lastGenerationTime) < 30.0 {
            let timeSinceLastRequest = currentTime.timeIntervalSince(lastGenerationTime)
            print("⚠️ 检测到\(String(format: "%.1f", timeSinceLastRequest))秒内的重复请求，忽略: \(requestSignature)")
            return
        }
        
        // 取消之前的任务
        currentGenerationTask?.cancel()
        
        // 更新状态
        isGenerating = true
        lastGenerationRequest = requestSignature
        lastGenerationTime = currentTime
        
        print("🎯 开始生成自定义路线 - 起始位置: \(startLocation), 需求: \(requirements), 距离: \(Int(minDistance))-\(Int(maxDistance))公里")
        print("🔑 请求标识: \(requestSignature)")
        
        currentGenerationTask = Task { @MainActor in
            defer {
                self.isGenerating = false
                self.currentGenerationTask = nil
            }

            // 清除之前的错误状态
            self.error = nil

            do {
                print("📡 调用DeepSeek API生成路线...")
                let routes = try await deepSeekManager.generateCustomExploreRoutes(
                    startLocation: startLocation,
                    requirements: requirements,
                    minDistance: minDistance,
                    maxDistance: maxDistance,
                    count: 2  // 固定生成2个路线
                )

                // 检查任务是否被取消
                if Task.isCancelled {
                    print("🚫 路线生成任务被取消")
                    return
                }

                // 验证路线质量
                guard !routes.isEmpty else {
                    throw ExploreError.noRoutesGenerated
                }

                print("🔄 DeepSeek返回 \(routes.count) 条路线，开始验证轨迹数据...")

                // 检查每条路线的轨迹数据
                var validRoutes: [ExploreActivity] = []
                for (index, route) in routes.enumerated() {
                    print("📊 路线 \(index + 1): \(route.title)")
                    print("  - 途径点数量: \(route.waypoints.count)")
                    print("  - 轨迹点数量: \(route.gpxTrack.count)")
                    print("  - 距离: \(route.distance) km")

                    // 验证路线数据完整性
                    if route.waypoints.isEmpty {
                        print("  ⚠️ 此路线没有途径点数据，跳过")
                        continue
                    }

                    if route.gpxTrack.isEmpty {
                        print("  ⚠️ 此路线没有轨迹数据，但保留（可能使用fallback）")
                    } else if route.gpxTrack.count < 5 {
                        print("  ⚠️ 轨迹点数量较少，但保留")
                    } else {
                        print("  ✅ 轨迹数据正常")
                    }

                    // 输出途径点信息
                    for (i, waypoint) in route.waypoints.enumerated() {
                        print("    途径点 \(i + 1): \(waypoint.name) (\(waypoint.type))")
                    }

                    // 输出前几个轨迹点用于调试
                    for (i, point) in route.gpxTrack.prefix(3).enumerated() {
                        print("    轨迹点 \(i + 1): (\(String(format: "%.6f", point.latitude)), \(String(format: "%.6f", point.longitude))) - \(point.name ?? "未命名")")
                    }

                    validRoutes.append(route)
                }

                // 再次检查任务是否被取消
                if Task.isCancelled {
                    print("🚫 路线生成任务在处理阶段被取消")
                    return
                }

                // 确保至少有一条有效路线
                guard !validRoutes.isEmpty else {
                    throw ExploreError.noValidRoutes
                }

                // 只有在成功验证后才更新UI状态
                self.exploreActivities = validRoutes
                self.error = nil

                print("✅ 成功生成并验证 \(validRoutes.count) 条自定义探索路线")

            } catch {
                if !Task.isCancelled {
                    let errorMessage = "生成自定义路线失败: \(error.localizedDescription)"
                    self.error = errorMessage
                    self.exploreActivities.removeAll() // 清除可能的部分数据
                    print("❌ 生成自定义路线失败: \(error)")
                }
            }
        }
    }
    
    func clearRoutes() {
        exploreActivities.removeAll()
        hasGeneratedForCurrentLocation = false
        error = nil
    }
    
    func saveRouteAsActivity(_ exploreActivity: ExploreActivity) {
        // 将探索路线保存为正式活动
        // 这里可以集成到ActivityManager中
        print("💾 保存路线为活动: \(exploreActivity.title)")
        
        // TODO: 集成到ActivityManager，创建正式的骑行活动
    }
    
    // MARK: - 数据持久化（可选）
    private let userDefaults = UserDefaults.standard
    private let exploreDataKey = "SavedExploreActivities"
    
    func loadSavedRoutes() {
        guard let data = userDefaults.data(forKey: exploreDataKey),
              let savedRoutes = try? JSONDecoder().decode([ExploreActivity].self, from: data) else {
            return
        }
        
        exploreActivities = savedRoutes
        print("📱 加载了\(savedRoutes.count)条已保存的探索路线")
    }
    
    func saveRoutesToLocal() {
        guard let data = try? JSONEncoder().encode(exploreActivities) else {
            print("❌ 无法编码探索路线数据")
            return
        }
        
        userDefaults.set(data, forKey: exploreDataKey)
        print("💾 已保存\(exploreActivities.count)条探索路线到本地")
    }
}