import Foundation
import Combine
import CoreLocation

// MARK: - 探索错误类型
enum ExploreError: LocalizedError {
    case noRoutesGenerated
    case noValidRoutes
    case invalidLocationData

    var errorDescription: String? {
        switch self {
        case .noRoutesGenerated:
            return "未能生成任何路线，请检查网络连接或重试"
        case .noValidRoutes:
            return "生成的路线数据不完整，请重新生成"
        case .invalidLocationData:
            return "位置信息无效，请重新选择起始位置"
        }
    }
}

@MainActor
class ExploreManager: ObservableObject {
    static let shared = ExploreManager()
    
    @Published var exploreActivities: [ExploreActivity] = []
    @Published var isLoading: Bool = false
    @Published var error: String?
    @Published var hasGeneratedForCurrentLocation: Bool = false
    
    private let locationManager = ExploreLocationManager.shared
    private let deepSeekManager = DeepSeekAPIManager.shared
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - 任务管理
    private var currentGenerationTask: Task<Void, Never>?
    private var isGenerating = false
    private var lastGenerationRequest: String = ""  // 用于防止重复请求
    private var lastGenerationTime: Date = Date.distantPast  // 记录上次生成时间
    
    private init() {
        setupBindings()
    }
    
    private func setupBindings() {
        // 监听位置变化
        locationManager.$currentCity
            .sink { [weak self] (city: String) in
                if !city.isEmpty && !(self?.hasGeneratedForCurrentLocation ?? false) {
                    Task {
                        await self?.generateRoutesForCurrentLocation()
                    }
                }
            }
            .store(in: &cancellables)
        
        // 监听DeepSeek API状态
        deepSeekManager.$isGenerating
            .assign(to: \.isLoading, on: self)
            .store(in: &cancellables)
        
        deepSeekManager.$error
            .compactMap { $0?.localizedDescription }
            .assign(to: \.error, on: self)
            .store(in: &cancellables)
    }
    
    func startExploring() {
        guard !isLoading else { return }
        
        // 首先获取位置
        if locationManager.currentCity.isEmpty {
            locationManager.requestLocationPermission()
        } else {
            Task {
                await generateRoutesForCurrentLocation()
            }
        }
    }
    
    func refreshRoutes() {
        Task {
            await generateRoutesForCurrentLocation(forceRefresh: true)
        }
    }
    
    private func generateRoutesForCurrentLocation(forceRefresh: Bool = false) async {
        let city = locationManager.currentCity
        
        guard !city.isEmpty else {
            error = "无法获取当前城市信息"
            return
        }
        
        if !forceRefresh && hasGeneratedForCurrentLocation {
            return
        }
        
        do {
            let routes = try await deepSeekManager.generateExploreRoutes(for: city, count: 3)
            
            await MainActor.run {
                self.exploreActivities = routes
                self.hasGeneratedForCurrentLocation = true
                self.error = nil
                
                print("🎯 成功生成\(routes.count)条\(city)的探索路线")
                
                // 打印路线信息用于调试
                for route in routes {
                    print("📍 路线: \(route.title)")
                    print("   距离: \(route.distance)km")
                    print("   轨迹点数: \(route.gpxTrack.count)")
                }
            }
            
        } catch {
            await MainActor.run {
                self.error = "生成探索路线失败: \(error.localizedDescription)"
                print("❌ 生成路线失败: \(error)")
            }
        }
    }
    
    func generateMoreRoutes() {
        let city = locationManager.currentCity
        
        guard !city.isEmpty else {
            error = "无法获取当前城市信息"
            return
        }
        
        Task {
            do {
                let newRoutes = try await deepSeekManager.generateExploreRoutes(for: city, count: 2)
                
                await MainActor.run {
                    self.exploreActivities.append(contentsOf: newRoutes)
                    self.error = nil
                    
                    print("➕ 新增\(newRoutes.count)条探索路线")
                }
                
            } catch {
                await MainActor.run {
                    self.error = "生成更多路线失败: \(error.localizedDescription)"
                }
            }
        }
    }
    
    func generateCustomRoutes(startLocation: String, requirements: String, minDistance: Double = 5.0, maxDistance: Double = 25.0) {
        // 生成请求标识符用于去重
        let requestSignature = "\(startLocation)-\(requirements)-\(Int(minDistance))-\(Int(maxDistance))"
        let currentTime = Date()
        
        // 防止重复生成：检查任务状态、请求相似性和时间间隔
        if isGenerating {
            print("⚠️ 已有路线生成任务在进行中，忽略重复请求")
            return
        }
        
        // 防止短时间内的重复请求（30秒内相同请求）
        if requestSignature == lastGenerationRequest && 
           currentTime.timeIntervalSince(lastGenerationTime) < 30.0 {
            let timeSinceLastRequest = currentTime.timeIntervalSince(lastGenerationTime)
            print("⚠️ 检测到\(String(format: "%.1f", timeSinceLastRequest))秒内的重复请求，忽略: \(requestSignature)")
            return
        }
        
        // 取消之前的任务
        currentGenerationTask?.cancel()
        
        // 更新状态
        isGenerating = true
        lastGenerationRequest = requestSignature
        lastGenerationTime = currentTime
        
        print("🎯 开始生成自定义路线 - 起始位置: \(startLocation), 需求: \(requirements), 距离: \(Int(minDistance))-\(Int(maxDistance))公里")
        print("🔑 请求标识: \(requestSignature)")
        
        currentGenerationTask = Task { @MainActor in
            defer {
                self.isGenerating = false
                self.currentGenerationTask = nil
            }

            // 清除之前的错误状态和路线数据
            self.error = nil
            self.exploreActivities.removeAll()

            do {
                print("📡 第1步: 调用DeepSeek API生成路线推荐...")
                let routes = try await deepSeekManager.generateCustomExploreRoutes(
                    startLocation: startLocation,
                    requirements: requirements,
                    minDistance: minDistance,
                    maxDistance: maxDistance,
                    count: 2  // 固定生成2个路线
                )

                // 检查任务是否被取消
                if Task.isCancelled {
                    print("🚫 路线生成任务被取消")
                    return
                }

                // 验证路线质量
                guard !routes.isEmpty else {
                    throw ExploreError.noRoutesGenerated
                }

                print("✅ 第1步完成: DeepSeek返回 \(routes.count) 条路线推荐")
                print("📍 第2步: 验证路线数据完整性...")

                // 检查每条路线的轨迹数据
                var validRoutes: [ExploreActivity] = []
                for (index, route) in routes.enumerated() {
                    print("📊 验证路线 \(index + 1): \(route.title)")
                    print("  - 途径点: \(route.waypoints.map { $0.name }.joined(separator: " → "))")
                    print("  - 轨迹点数量: \(route.gpxTrack.count)")
                    print("  - 距离: \(route.distance) km")

                    // 验证路线数据完整性
                    if route.waypoints.isEmpty {
                        print("  ❌ 此路线没有途径点数据，跳过")
                        continue
                    }

                    if route.gpxTrack.isEmpty {
                        print("  ⚠️ 此路线没有轨迹数据，但保留（使用了fallback算法）")
                    } else if route.gpxTrack.count < 5 {
                        print("  ⚠️ 轨迹点数量较少(\(route.gpxTrack.count)个)，但保留")
                    } else {
                        print("  ✅ 轨迹数据正常(\(route.gpxTrack.count)个点)")
                    }

                    validRoutes.append(route)
                }

                // 再次检查任务是否被取消
                if Task.isCancelled {
                    print("🚫 路线生成任务在验证阶段被取消")
                    return
                }

                // 确保至少有一条有效路线
                guard !validRoutes.isEmpty else {
                    throw ExploreError.noValidRoutes
                }

                print("✅ 第2步完成: 验证通过，共 \(validRoutes.count) 条有效路线")

                // 只有在成功验证后才更新UI状态
                self.exploreActivities = validRoutes
                self.error = nil

                print("🎉 路线生成完成! 成功生成 \(validRoutes.count) 条自定义探索路线")

                // 输出最终结果摘要
                for (index, route) in validRoutes.enumerated() {
                    print("📋 路线 \(index + 1): \(route.title)")
                    print("   途径点: \(route.waypoints.map { $0.name }.joined(separator: " → "))")
                    print("   距离: \(route.distance)km, 难度: \(route.difficulty)")
                }

                // 标记生成完成，停止所有后续处理
                print("🏁 路线生成流程完全结束")

            } catch {
                if !Task.isCancelled {
                    // 检查是否是API解析错误（这种情况下DeepSeek会自动重试）
                    if let apiError = error as? APIError,
                       case .parseError = apiError {
                        print("🔄 检测到解析错误，DeepSeek正在智能重试中...")
                        // 不设置错误状态，让重试机制继续工作
                        // 等待一段时间让重试机制完成
                        try? await Task.sleep(nanoseconds: 2_000_000_000) // 等待2秒

                        // 如果重试仍在进行，继续等待
                        var waitTime = 0
                        while waitTime < 300 { // 最多等待5分钟
                            try? await Task.sleep(nanoseconds: 1_000_000_000) // 每秒检查一次
                            waitTime += 1

                            // 检查是否有新的路线生成
                            if !self.exploreActivities.isEmpty {
                                print("✅ 重试成功，已生成路线")
                                return
                            }

                            // 检查任务是否被取消
                            if Task.isCancelled {
                                return
                            }
                        }

                        // 如果等待超时，设置错误状态
                        print("⏰ 重试等待超时")
                        let errorMessage = "智能重试超时，请检查网络连接后重试"
                        self.error = errorMessage
                        return
                    }

                    // 其他类型的错误才设置错误状态
                    let errorMessage = "生成自定义路线失败: \(error.localizedDescription)"
                    self.error = errorMessage
                    self.exploreActivities.removeAll() // 清除可能的部分数据
                    print("❌ 路线生成失败: \(error)")

                    // 提供更详细的错误信息
                    if let exploreError = error as? ExploreError {
                        switch exploreError {
                        case .noRoutesGenerated:
                            print("💡 建议: 尝试调整搜索条件或距离范围")
                        case .noValidRoutes:
                            print("💡 建议: 检查网络连接或稍后重试")
                        case .invalidLocationData:
                            print("💡 建议: 检查起始位置是否正确")
                        }
                    }
                }
            }
        }
    }
    
    func clearRoutes() {
        exploreActivities.removeAll()
        hasGeneratedForCurrentLocation = false
        error = nil
    }
    
    func saveRouteAsActivity(_ exploreActivity: ExploreActivity) {
        // 将探索路线保存为正式活动
        // 这里可以集成到ActivityManager中
        print("💾 保存路线为活动: \(exploreActivity.title)")
        
        // TODO: 集成到ActivityManager，创建正式的骑行活动
    }
    
    // MARK: - 数据持久化（可选）
    private let userDefaults = UserDefaults.standard
    private let exploreDataKey = "SavedExploreActivities"
    
    func loadSavedRoutes() {
        guard let data = userDefaults.data(forKey: exploreDataKey),
              let savedRoutes = try? JSONDecoder().decode([ExploreActivity].self, from: data) else {
            return
        }
        
        exploreActivities = savedRoutes
        print("📱 加载了\(savedRoutes.count)条已保存的探索路线")
    }
    
    func saveRoutesToLocal() {
        guard let data = try? JSONEncoder().encode(exploreActivities) else {
            print("❌ 无法编码探索路线数据")
            return
        }
        
        userDefaults.set(data, forKey: exploreDataKey)
        print("💾 已保存\(exploreActivities.count)条探索路线到本地")
    }
}