import Foundation
import CoreLocation
import Network

// MARK: - 地理编码错误信息
struct GeocodeError {
    let locationName: String
    let address: String
    let errorDescription: String
    let suggestedFix: String
}

// MARK: - 路线生成结果
struct RouteGenerationResult {
    let activity: ExploreActivity
    let geocodeErrors: [GeocodeError]
}

// MARK: - 路线生成器
class RouteGenerator {
    static let shared = RouteGenerator()

    private let geocoder = CLGeocoder()
    private let directionsService = AppleMapDirectionsService.shared
    private let networkMonitor = NWPathMonitor()
    private let networkQueue = DispatchQueue(label: "NetworkMonitor")
    private var isNetworkAvailable = true

    private init() {
        setupNetworkMonitoring()
    }

    /// 设置网络监控
    private func setupNetworkMonitoring() {
        networkMonitor.pathUpdateHandler = { [weak self] path in
            DispatchQueue.main.async {
                self?.isNetworkAvailable = path.status == .satisfied
                if !(self?.isNetworkAvailable ?? true) {
                    print("🌐 网络连接不可用")
                } else {
                    print("🌐 网络连接已恢复")
                }
            }
        }
        networkMonitor.start(queue: networkQueue)
    }

    deinit {
        networkMonitor.cancel()
    }
    
    /// 从路线推荐生成完整的ExploreActivity
    func generateRoute(from recommendation: RouteRecommendation, city: String) async throws -> ExploreActivity {
        print("🔄 开始生成路线: \(recommendation.name)")

        // 1. 地理编码获取真实坐标
        let waypoints = try await geocodeWaypoints(recommendation.waypoints, city: city)
        print("📍 地理编码完成，获得\(waypoints.count)个有效坐标")

        // 2. 生成轨迹
        let trackPoints = try await generateTrackPoints(for: waypoints, city: city)
        print("🛤️ 轨迹生成完成，包含\(trackPoints.count)个点")

        // 3. 创建ExploreActivity
        let distance = extractDistanceValue(from: recommendation.distance)

        let activity = ExploreActivity(
            title: recommendation.name,
            description: recommendation.description,
            distance: distance,
            duration: recommendation.duration,
            difficulty: recommendation.difficulty,
            highlights: recommendation.highlights,
            roadCondition: recommendation.roadCondition,
            bestTime: recommendation.bestTime,
            waypoints: waypoints,
            gpxTrack: trackPoints,
            city: city
        )

        print("✅ 路线生成完成: \(recommendation.name)")
        return activity
    }

    /// 从路线推荐生成完整的ExploreActivity（带错误收集）
    func generateRouteWithErrorCollection(from recommendation: RouteRecommendation, city: String) async throws -> RouteGenerationResult {
        print("🔄 开始生成路线: \(recommendation.name)")

        // 1. 地理编码获取真实坐标（收集错误）
        let (waypoints, geocodeErrors) = try await geocodeWaypointsWithErrorCollection(recommendation.waypoints, city: city)
        print("📍 地理编码完成，获得\(waypoints.count)个有效坐标，\(geocodeErrors.count)个错误")

        // 2. 生成轨迹
        let trackPoints = try await generateTrackPoints(for: waypoints, city: city)
        print("🛤️ 轨迹生成完成，包含\(trackPoints.count)个点")

        // 3. 创建ExploreActivity
        let distance = extractDistanceValue(from: recommendation.distance)

        let activity = ExploreActivity(
            title: recommendation.name,
            description: recommendation.description,
            distance: distance,
            duration: recommendation.duration,
            difficulty: recommendation.difficulty,
            highlights: recommendation.highlights,
            roadCondition: recommendation.roadCondition,
            bestTime: recommendation.bestTime,
            waypoints: waypoints,
            gpxTrack: trackPoints,
            city: city
        )

        print("✅ 路线生成完成: \(recommendation.name)")
        return RouteGenerationResult(activity: activity, geocodeErrors: geocodeErrors)
    }
    
    // MARK: - 私有方法
    
    /// 地理编码获取真实坐标
    private func geocodeWaypoints(_ simpleWaypoints: [SimpleWaypoint], city: String) async throws -> [Waypoint] {
        var waypoints: [Waypoint] = []

        for (_, simpleWaypoint) in simpleWaypoints.enumerated() {
            print("🔍 地理编码: \(simpleWaypoint.name)")

            let coordinate = try await geocodeLocation(
                name: simpleWaypoint.name,
                address: simpleWaypoint.address,
                city: city
            )

            let waypoint = Waypoint(
                name: simpleWaypoint.name,
                address: simpleWaypoint.address,
                description: simpleWaypoint.description,
                type: simpleWaypoint.type,
                latitude: coordinate.latitude,
                longitude: coordinate.longitude
            )

            waypoints.append(waypoint)
            print("✅ \(simpleWaypoint.name): (\(String(format: "%.6f", coordinate.latitude)), \(String(format: "%.6f", coordinate.longitude)))")
        }

        return waypoints
    }

    /// 地理编码获取真实坐标（带错误收集）
    private func geocodeWaypointsWithErrorCollection(_ simpleWaypoints: [SimpleWaypoint], city: String) async throws -> ([Waypoint], [GeocodeError]) {
        var waypoints: [Waypoint] = []
        var geocodeErrors: [GeocodeError] = []

        for (_, simpleWaypoint) in simpleWaypoints.enumerated() {
            print("🔍 地理编码: \(simpleWaypoint.name)")

            let (coordinate, error) = await geocodeLocationWithErrorCollection(
                name: simpleWaypoint.name,
                address: simpleWaypoint.address,
                city: city
            )

            let waypoint = Waypoint(
                name: simpleWaypoint.name,
                address: simpleWaypoint.address,
                description: simpleWaypoint.description,
                type: simpleWaypoint.type,
                latitude: coordinate.latitude,
                longitude: coordinate.longitude
            )

            waypoints.append(waypoint)

            if let error = error {
                geocodeErrors.append(error)
                print("⚠️ \(simpleWaypoint.name): 使用fallback坐标，错误: \(error.errorDescription)")
            } else {
                print("✅ \(simpleWaypoint.name): (\(String(format: "%.6f", coordinate.latitude)), \(String(format: "%.6f", coordinate.longitude)))")
            }
        }

        return (waypoints, geocodeErrors)
    }
    
    /// 地理编码单个位置
    private func geocodeLocation(name: String, address: String, city: String) async throws -> CLLocationCoordinate2D {
        // 检查网络连接
        if !isNetworkAvailable {
            print("🌐 网络不可用，直接使用fallback坐标: \(name)")
            return generateSmartFallbackCoordinate(for: name, city: city)
        }

        // 构建查询字符串
        let queries = [
            "\(name), \(city)",
            "\(address), \(city)",
            address.contains(city) ? address : "\(address), \(city)"
        ].filter { !$0.isEmpty }

        // 尝试地理编码，带重试机制
        for (queryIndex, query) in queries.enumerated() {
            for retryCount in 0..<3 { // 每个查询最多重试3次
                do {
                    print("🔍 地理编码尝试 \(retryCount + 1)/3: \(query)")

                    let placemarks = try await geocoder.geocodeAddressString(query)
                    if let placemark = placemarks.first,
                       let location = placemark.location {
                        let coordinate = location.coordinate

                        // 验证坐标是否合理
                        if isValidCoordinate(coordinate, city: city) {
                            print("✅ 地理编码成功: \(name) -> (\(String(format: "%.6f", coordinate.latitude)), \(String(format: "%.6f", coordinate.longitude)))")
                            return coordinate
                        } else {
                            print("⚠️ 坐标超出城市范围: \(coordinate)")
                        }
                    }
                } catch {
                    let nsError = error as NSError
                    print("⚠️ 地理编码失败: \(query) - \(error.localizedDescription)")

                    // 检查错误类型
                    if nsError.domain == kCLErrorDomain {
                        switch nsError.code {
                        case CLError.network.rawValue:
                            print("🌐 网络错误，等待后重试...")
                            if retryCount < 2 {
                                try await Task.sleep(nanoseconds: UInt64((retryCount + 1) * 2_000_000_000)) // 递增等待时间
                                continue
                            }
                        case -3: // GEOErrorDomain 限流错误
                            print("⏱️ 地理编码API限流，等待更长时间...")
                            if retryCount < 2 {
                                // 限流时等待更长时间
                                let waitTime = UInt64((retryCount + 1) * 5_000_000_000) // 5秒、10秒递增
                                try await Task.sleep(nanoseconds: waitTime)
                                continue
                            }
                        case CLError.geocodeCanceled.rawValue:
                            print("🚫 地理编码被取消")
                            break
                        case CLError.geocodeFoundNoResult.rawValue:
                            print("🔍 未找到结果，尝试下一个查询")
                            break
                        case CLError.geocodeFoundPartialResult.rawValue:
                            print("📍 找到部分结果，尝试下一个查询")
                            break
                        default:
                            print("❌ 其他地理编码错误: \(nsError.code)")
                        }
                    }

                    if retryCount == 2 {
                        break // 最后一次重试失败，尝试下一个查询
                    }
                }

                // 避免频率限制
                if retryCount < 2 {
                    try await Task.sleep(nanoseconds: 2_000_000_000) // 增加到2秒
                }
            }

            // 查询间隔 - 增加间隔避免限流
            if queryIndex < queries.count - 1 {
                try await Task.sleep(nanoseconds: 2_000_000_000) // 增加到2秒
            }
        }

        // 如果所有尝试都失败，生成智能fallback坐标
        print("🎲 使用智能fallback坐标: \(name)")
        return generateSmartFallbackCoordinate(for: name, city: city)
    }

    /// 地理编码单个位置（带错误收集）
    private func geocodeLocationWithErrorCollection(name: String, address: String, city: String) async -> (CLLocationCoordinate2D, GeocodeError?) {
        // 检查网络连接
        if !isNetworkAvailable {
            print("🌐 网络不可用，直接使用fallback坐标: \(name)")
            let error = GeocodeError(
                locationName: name,
                address: address,
                errorDescription: "网络连接不可用",
                suggestedFix: "请提供更具体的地址或使用知名地标"
            )
            return (generateSmartFallbackCoordinate(for: name, city: city), error)
        }

        // 构建查询字符串
        let queries = [
            "\(name), \(city)",
            "\(address), \(city)",
            address.contains(city) ? address : "\(address), \(city)"
        ].filter { !$0.isEmpty }

        var lastError: Error?
        var isThrottled = false

        // 尝试地理编码，带重试机制
        for (queryIndex, query) in queries.enumerated() {
            for retryCount in 0..<3 { // 每个查询最多重试3次
                do {
                    print("🔍 地理编码尝试 \(retryCount + 1)/3: \(query)")

                    let placemarks = try await geocoder.geocodeAddressString(query)
                    if let placemark = placemarks.first,
                       let location = placemark.location {
                        let coordinate = location.coordinate

                        // 验证坐标是否合理
                        if isValidCoordinate(coordinate, city: city) {
                            print("✅ 地理编码成功: \(name) -> (\(String(format: "%.6f", coordinate.latitude)), \(String(format: "%.6f", coordinate.longitude)))")
                            return (coordinate, nil)
                        } else {
                            print("⚠️ 坐标超出城市范围: \(coordinate)")
                        }
                    }
                } catch {
                    lastError = error
                    let nsError = error as NSError
                    print("⚠️ 地理编码失败: \(query) - \(error.localizedDescription)")

                    // 检查错误类型
                    if nsError.domain == kCLErrorDomain {
                        switch nsError.code {
                        case CLError.network.rawValue:
                            print("🌐 网络错误，等待后重试...")
                            if retryCount < 2 {
                                try? await Task.sleep(nanoseconds: UInt64((retryCount + 1) * 2_000_000_000))
                                continue
                            }
                        case -3: // GEOErrorDomain 限流错误
                            print("⏱️ 地理编码API限流，等待更长时间...")
                            isThrottled = true
                            if retryCount < 2 {
                                let waitTime = UInt64((retryCount + 1) * 5_000_000_000)
                                try? await Task.sleep(nanoseconds: waitTime)
                                continue
                            }
                        case CLError.geocodeCanceled.rawValue:
                            print("🚫 地理编码被取消")
                            break
                        case CLError.geocodeFoundNoResult.rawValue:
                            print("🔍 未找到结果，尝试下一个查询")
                            break
                        case CLError.geocodeFoundPartialResult.rawValue:
                            print("📍 找到部分结果，尝试下一个查询")
                            break
                        default:
                            print("❌ 其他地理编码错误: \(nsError.code)")
                        }
                    }

                    if retryCount == 2 {
                        break // 最后一次重试失败，尝试下一个查询
                    }
                }

                // 避免频率限制
                if retryCount < 2 {
                    try? await Task.sleep(nanoseconds: 2_000_000_000)
                }
            }

            // 查询间隔 - 增加间隔避免限流
            if queryIndex < queries.count - 1 {
                try? await Task.sleep(nanoseconds: 2_000_000_000)
            }
        }

        // 如果所有尝试都失败，生成错误信息和fallback坐标
        print("🎲 使用智能fallback坐标: \(name)")

        let errorDescription: String
        let suggestedFix: String

        if isThrottled {
            errorDescription = "地理编码API限流"
            suggestedFix = "请使用更知名的地标或详细地址，避免使用过于具体的小地点"
        } else if let lastError = lastError {
            errorDescription = lastError.localizedDescription
            suggestedFix = "地点'\(name)'无法找到，建议使用更知名的地标或提供更详细的地址"
        } else {
            errorDescription = "未找到匹配的地点"
            suggestedFix = "请提供更具体或更知名的地点名称"
        }

        let error = GeocodeError(
            locationName: name,
            address: address,
            errorDescription: errorDescription,
            suggestedFix: suggestedFix
        )

        return (generateSmartFallbackCoordinate(for: name, city: city), error)
    }
    
    /// 验证坐标是否合理
    private func isValidCoordinate(_ coordinate: CLLocationCoordinate2D, city: String) -> Bool {
        let cityInfo = getCityInfo(city)
        let cityCenter = CLLocation(latitude: cityInfo.latitude, longitude: cityInfo.longitude)
        let coordinateLocation = CLLocation(latitude: coordinate.latitude, longitude: coordinate.longitude)
        let distance = cityCenter.distance(from: coordinateLocation) / 1000.0 // 转换为公里
        
        let maxDistance = cityInfo.maxRadius * 111.0 // 转换为公里
        return distance <= maxDistance
    }
    
    /// 生成智能fallback坐标
    private func generateSmartFallbackCoordinate(for name: String, city: String) -> CLLocationCoordinate2D {
        let cityInfo = getCityInfo(city)
        
        // 根据地点名称特征调整位置
        var offset: (lat: Double, lng: Double) = (0, 0)
        
        if name.contains("公园") || name.contains("湖") {
            offset = (0.02, 0.02) // 公园通常在城市边缘
        } else if name.contains("商场") || name.contains("购物") {
            offset = (0.005, 0.005) // 商场通常在市中心
        } else if name.contains("路") || name.contains("街") {
            offset = (0.015, 0.015) // 道路分布较广
        } else {
            offset = (Double.random(in: -0.02...0.02), Double.random(in: -0.02...0.02))
        }
        
        let latitude = cityInfo.latitude + offset.lat
        let longitude = cityInfo.longitude + offset.lng
        
        return CLLocationCoordinate2D(latitude: latitude, longitude: longitude)
    }
    
    /// 生成轨迹点
    private func generateTrackPoints(for waypoints: [Waypoint], city: String) async throws -> [ExploreTrackPoint] {
        // 首先尝试使用Apple Maps生成真实路线
        do {
            print("🗺️ 尝试使用Apple Maps生成真实轨迹...")
            let trackPoints = try await directionsService.generateCyclingRoute(from: waypoints, cityContext: city)
            print("✅ Apple Maps成功生成\(trackPoints.count)个轨迹点")
            return trackPoints
        } catch {
            print("⚠️ Apple Maps失败: \(error.localizedDescription)")
            print("🔄 使用智能fallback算法...")
            
            // 使用智能fallback算法
            return generateSmartTrackPoints(for: waypoints)
        }
    }
    
    /// 生成智能轨迹点
    private func generateSmartTrackPoints(for waypoints: [Waypoint]) -> [ExploreTrackPoint] {
        var trackPoints: [ExploreTrackPoint] = []
        
        for i in 0..<waypoints.count {
            let waypoint = waypoints[i]
            
            // 添加waypoint本身
            let waypointTrackPoint = ExploreTrackPoint(
                latitude: waypoint.latitude,
                longitude: waypoint.longitude,
                elevation: 500.0 + Double.random(in: -20...30),
                name: waypoint.name
            )
            trackPoints.append(waypointTrackPoint)
            
            // 如果不是最后一个点，生成到下一个点的路径
            if i < waypoints.count - 1 {
                let nextWaypoint = waypoints[i + 1]
                let pathPoints = generatePathBetween(
                    from: waypoint,
                    to: nextWaypoint
                )
                trackPoints.append(contentsOf: pathPoints)
            }
        }
        
        print("🛤️ 智能fallback生成\(trackPoints.count)个轨迹点")
        return trackPoints
    }
    
    /// 生成两点之间的路径
    private func generatePathBetween(from start: Waypoint, to end: Waypoint) -> [ExploreTrackPoint] {
        let distance = sqrt(pow(end.latitude - start.latitude, 2) + pow(end.longitude - start.longitude, 2))
        let segmentCount = max(3, min(15, Int(distance * 1000))) // 根据距离调整点数
        
        var pathPoints: [ExploreTrackPoint] = []
        
        for i in 1..<segmentCount {
            let progress = Double(i) / Double(segmentCount)
            
            // 基础线性插值
            let lat = start.latitude + (end.latitude - start.latitude) * progress
            let lng = start.longitude + (end.longitude - start.longitude) * progress
            
            // 添加轻微的弯曲效果
            let curvature = sin(progress * Double.pi) * 0.001
            let perpAngle = atan2(end.longitude - start.longitude, end.latitude - start.latitude) + Double.pi / 2
            
            let finalLat = lat + curvature * cos(perpAngle) + Double.random(in: -0.0002...0.0002)
            let finalLng = lng + curvature * sin(perpAngle) + Double.random(in: -0.0002...0.0002)
            
            let elevation = 500.0 + sin(progress * Double.pi * 2) * 10 + Double.random(in: -5...5)
            
            let pathPoint = ExploreTrackPoint(
                latitude: finalLat,
                longitude: finalLng,
                elevation: elevation,
                name: "路径点"
            )
            
            pathPoints.append(pathPoint)
        }
        
        return pathPoints
    }
    
    /// 从距离字符串中提取数值
    private func extractDistanceValue(from distanceString: String) -> Double {
        let numbers = distanceString.components(separatedBy: CharacterSet.decimalDigits.inverted).joined()
        return Double(numbers) ?? 15.0
    }
    
    /// 获取城市信息
    private func getCityInfo(_ city: String) -> (latitude: Double, longitude: Double, maxRadius: Double) {
        if city.contains("成都") {
            return (30.6532, 104.0665, 0.12)
        } else if city.contains("北京") {
            return (39.9042, 116.4074, 0.20)
        } else if city.contains("上海") {
            return (31.2304, 121.4737, 0.18)
        } else if city.contains("广州") {
            return (23.1291, 113.2644, 0.15)
        } else if city.contains("深圳") {
            return (22.5431, 114.0579, 0.12)
        } else if city.contains("杭州") {
            return (30.2741, 120.1551, 0.12)
        } else {
            return (30.6532, 104.0665, 0.12) // 默认成都
        }
    }
}
