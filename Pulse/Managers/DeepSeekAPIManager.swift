import Foundation
import CoreLocation

// MARK: - 路线推荐数据模型
struct RouteRecommendation: Codable {
    let name: String
    let description: String
    let distance: String
    let duration: String
    let difficulty: String
    let highlights: [String]
    let roadCondition: String
    let bestTime: String
    let waypoints: [SimpleWaypoint]
}



struct RoutesResponse: Codable {
    let routes: [RouteRecommendation]
}

// MARK: - API数据结构
struct ChatCompletionRequest: Codable {
    let model: String
    let messages: [ChatMessage]
    let temperature: Double
    let max_tokens: Int
    let stream: Bool
}

struct ChatMessage: Codable {
    let role: String
    let content: String
}

struct ChatCompletionResponse: Codable {
    let choices: [Choice]

    struct Choice: Codable {
        let message: ChatMessage
    }
}

enum APIError: Error, LocalizedError {
    case invalidURL
    case invalidResponse
    case serverError(Int, String)
    case parseError(String)

    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "无效的URL"
        case .invalidResponse:
            return "无效的响应"
        case .serverError(let code, let message):
            return "服务器错误 \(code): \(message)"
        case .parseError(let message):
            return "解析错误: \(message)"
        }
    }
}

// MARK: - DeepSeek API Manager
@MainActor
class DeepSeekAPIManager: ObservableObject {
    static let shared = DeepSeekAPIManager()

    @Published var isGenerating = false
    @Published var error: Error?

    private let apiKey = "sk-eefc087f46024597bc2bf5837a9cba49"
    private let baseURL = "https://api.deepseek.com/chat/completions"

    private init() {}

    // MARK: - 主要API方法

    /// 为指定城市生成探索路线
    func generateExploreRoutes(for city: String, count: Int = 2) async throws -> [ExploreActivity] {
        print("🚀 开始为\(city)生成\(count)条探索路线")

        isGenerating = true
        error = nil

        defer {
            isGenerating = false
        }

        do {
            // 1. 调用DeepSeek API获取路线推荐
            let routeRecommendations = try await fetchRouteRecommendations(city: city, count: count)
            print("✅ DeepSeek返回\(routeRecommendations.count)条路线推荐")

            // 2. 为每条路线生成真实轨迹
            var activities: [ExploreActivity] = []
            for (index, recommendation) in routeRecommendations.enumerated() {
                print("📍 处理路线 \(index + 1): \(recommendation.name)")

                let activity = try await RouteGenerator.shared.generateRoute(
                    from: recommendation,
                    city: city
                )
                activities.append(activity)
            }

            print("🎉 成功生成\(activities.count)条完整路线")
            return activities

        } catch {
            print("❌ 路线生成失败: \(error)")
            self.error = error
            throw error
        }
    }

    /// 生成自定义路线
    func generateCustomExploreRoutes(
        startLocation: String,
        requirements: String,
        minDistance: Double = 5.0,
        maxDistance: Double = 25.0,
        count: Int = 2
    ) async throws -> [ExploreActivity] {
        print("🎯 开始生成自定义路线: \(startLocation) -> \(requirements)")

        isGenerating = true
        error = nil

        defer {
            isGenerating = false
        }

        do {
            // 1. 调用DeepSeek API获取自定义路线推荐
            let routeRecommendations = try await fetchCustomRouteRecommendations(
                startLocation: startLocation,
                requirements: requirements,
                minDistance: minDistance,
                maxDistance: maxDistance,
                count: count
            )
            print("✅ DeepSeek返回\(routeRecommendations.count)条自定义路线推荐")

            // 2. 为每条路线生成真实轨迹
            var activities: [ExploreActivity] = []
            for (index, recommendation) in routeRecommendations.enumerated() {
                print("📍 处理自定义路线 \(index + 1): \(recommendation.name)")

                let activity = try await RouteGenerator.shared.generateRoute(
                    from: recommendation,
                    city: startLocation
                )
                activities.append(activity)
            }

            print("🎉 成功生成\(activities.count)条自定义路线")
            return activities

        } catch {
            print("❌ 自定义路线生成失败: \(error)")
            self.error = error
            throw error
        }
    }

    // MARK: - 私有方法

    /// 获取路线推荐
    private func fetchRouteRecommendations(city: String, count: Int) async throws -> [RouteRecommendation] {
        let prompt = createCityRoutePrompt(city: city, count: count)
        let response = try await callDeepSeekAPI(prompt: prompt)
        return try parseRouteRecommendations(from: response)
    }

    /// 获取自定义路线推荐
    private func fetchCustomRouteRecommendations(
        startLocation: String,
        requirements: String,
        minDistance: Double,
        maxDistance: Double,
        count: Int
    ) async throws -> [RouteRecommendation] {
        let prompt = createCustomRoutePrompt(
            startLocation: startLocation,
            requirements: requirements,
            minDistance: minDistance,
            maxDistance: maxDistance,
            count: count
        )
        let response = try await callDeepSeekAPI(prompt: prompt)
        return try parseRouteRecommendations(from: response)
    }

    /// 调用DeepSeek API
    private func callDeepSeekAPI(prompt: String) async throws -> String {
        guard let url = URL(string: baseURL) else {
            throw APIError.invalidURL
        }

        let request = ChatCompletionRequest(
            model: "deepseek-chat",
            messages: [
                ChatMessage(role: "system", content: "你是专业的骑行路线规划师，返回包含详细途经点介绍的JSON格式路线信息。"),
                ChatMessage(role: "user", content: prompt)
            ],
            temperature: 0.8,
            max_tokens: 2000,
            stream: false
        )

        var urlRequest = URLRequest(url: url)
        urlRequest.httpMethod = "POST"
        urlRequest.setValue("application/json", forHTTPHeaderField: "Content-Type")
        urlRequest.setValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")
        urlRequest.httpBody = try JSONEncoder().encode(request)

        let (data, response) = try await URLSession.shared.data(for: urlRequest)

        guard let httpResponse = response as? HTTPURLResponse else {
            throw APIError.invalidResponse
        }

        guard httpResponse.statusCode == 200 else {
            throw APIError.serverError(httpResponse.statusCode, "HTTP \(httpResponse.statusCode)")
        }

        let chatResponse = try JSONDecoder().decode(ChatCompletionResponse.self, from: data)
        guard let content = chatResponse.choices.first?.message.content else {
            throw APIError.invalidResponse
        }

        return content
    }

    /// 解析路线推荐
    private func parseRouteRecommendations(from response: String) throws -> [RouteRecommendation] {
        let jsonString = extractJSON(from: response)
        guard let jsonData = jsonString.data(using: .utf8) else {
            throw APIError.parseError("无法转换为数据")
        }

        do {
            let routesResponse = try JSONDecoder().decode(RoutesResponse.self, from: jsonData)
            return routesResponse.routes
        } catch {
            print("❌ JSON解析失败: \(error)")
            print("📄 原始响应: \(response)")
            throw APIError.parseError("JSON解析失败: \(error.localizedDescription)")
        }
    }

    /// 从响应中提取JSON
    private func extractJSON(from content: String) -> String {
        var cleanContent = content
            .replacingOccurrences(of: "```json", with: "")
            .replacingOccurrences(of: "```", with: "")
            .trimmingCharacters(in: .whitespacesAndNewlines)

        // 查找JSON开始和结束
        if let startIndex = cleanContent.firstIndex(of: "{"),
           let endIndex = cleanContent.lastIndex(of: "}") {
            cleanContent = String(cleanContent[startIndex...endIndex])
        }

        return cleanContent
    }

    /// 创建城市路线prompt
    private func createCityRoutePrompt(city: String, count: Int) -> String {
        return """
        为\(city)生成\(count)条优质骑行路线。

        要求：
        1. 距离10-25公里
        2. 包含真实地点
        3. 每个waypoint都要有详细介绍

        JSON格式：
        {
          "routes": [
            {
              "name": "路线名称",
              "description": "路线特色和亮点描述",
              "distance": "15.5",
              "duration": "1-2小时",
              "difficulty": "简单",
              "highlights": ["景点1", "景点2", "景点3"],
              "roadCondition": "绿道/城市道路/混合路段",
              "bestTime": "最佳骑行时间段",
              "waypoints": [
                {
                  "name": "起点名称",
                  "address": "\(city)市具体详细地址",
                  "description": "详细介绍这个地点的特色、历史背景、适合骑行的原因等",
                  "type": "起点"
                },
                {
                  "name": "途经景点名称",
                  "address": "完整详细地址",
                  "description": "详细介绍景点特色、文化价值、游览建议、拍照点等",
                  "type": "景点"
                },
                {
                  "name": "休息点名称",
                  "address": "详细地址",
                  "description": "介绍休息点设施、周边环境、补给情况等",
                  "type": "途经点"
                },
                {
                  "name": "终点名称",
                  "address": "终点详细地址",
                  "description": "终点特色、结束后的活动建议、交通便利性等",
                  "type": "终点"
                }
              ]
            }
          ]
        }
        """
    }

    /// 创建自定义路线prompt
    private func createCustomRoutePrompt(
        startLocation: String,
        requirements: String,
        minDistance: Double,
        maxDistance: Double,
        count: Int
    ) -> String {
        let cityName = extractCityFromLocation(startLocation)

        return """
        基于用户需求生成\(count)条个性化骑行路线。

        起点：\(startLocation)
        用户需求：\(requirements)
        距离范围：\(Int(minDistance))-\(Int(maxDistance))公里

        JSON格式：
        {
          "routes": [
            {
              "name": "路线名称",
              "description": "路线特色和如何满足用户需求的描述",
              "distance": "15.5",
              "duration": "1-1.5小时",
              "difficulty": "中等",
              "highlights": ["特色点1", "特色点2", "特色点3"],
              "roadCondition": "道路类型详细说明",
              "bestTime": "最佳骑行时间",
              "waypoints": [
                {
                  "name": "起始位置",
                  "address": "\(startLocation)",
                  "description": "起点特色和出发准备建议",
                  "type": "起点"
                },
                {
                  "name": "目标景点",
                  "address": "\(cityName)市XX区具体详细地址",
                  "description": "详细介绍如何满足用户需求、景点特色、游览建议等",
                  "type": "景点"
                },
                {
                  "name": "中途休息点",
                  "address": "\(cityName)市XX区休息点地址",
                  "description": "休息设施、补给情况、周边环境介绍",
                  "type": "途经点"
                },
                {
                  "name": "终点位置",
                  "address": "\(cityName)市XX区终点详细地址",
                  "description": "终点特色、完成行程后的建议活动",
                  "type": "终点"
                }
              ]
            }
          ]
        }
        """
    }

    /// 从位置字符串中提取城市名称
    private func extractCityFromLocation(_ location: String) -> String {
        let components = location.components(separatedBy: CharacterSet(charactersIn: "市区县"))
        if let firstComponent = components.first, firstComponent.count > 1 {
            return firstComponent + "市"
        }
        return location
    }
}