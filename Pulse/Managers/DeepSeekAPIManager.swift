import Foundation
import CoreLocation

// MARK: - 路线推荐数据模型
struct RouteRecommendation: Codable {
    let name: String
    let description: String
    let distance: String
    let duration: String
    let difficulty: String
    let highlights: [String]
    let roadCondition: String
    let bestTime: String
    let waypoints: [SimpleWaypoint]
}



struct RoutesResponse: Codable {
    let routes: [RouteRecommendation]
}

// MARK: - API数据结构
struct ChatCompletionRequest: Codable {
    let model: String
    let messages: [ChatMessage]
    let temperature: Double
    let max_tokens: Int
    let stream: Bool
}

struct ChatMessage: Codable {
    let role: String
    let content: String
}

struct ChatCompletionResponse: Codable {
    let choices: [Choice]

    struct Choice: Codable {
        let message: ChatMessage
    }
}

enum APIError: Error, LocalizedError {
    case invalidURL
    case invalidResponse
    case serverError(Int, String)
    case parseError(String)

    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "无效的URL"
        case .invalidResponse:
            return "无效的响应"
        case .serverError(let code, let message):
            return "服务器错误 \(code): \(message)"
        case .parseError(let message):
            return "解析错误: \(message)"
        }
    }
}

// MARK: - 重试进度回调
typealias RetryProgressCallback = (Int, String) -> Void
typealias RetrySuccessCallback = () -> Void

// MARK: - DeepSeek API Manager
@MainActor
class DeepSeekAPIManager: ObservableObject {
    static let shared = DeepSeekAPIManager()

    @Published var isGenerating = false
    @Published var error: Error?

    private let apiKey = "sk-eefc087f46024597bc2bf5837a9cba49"
    private let baseURL = "https://api.deepseek.com/chat/completions"

    private init() {}

    // MARK: - 主要API方法

    /// 为指定城市生成探索路线
    func generateExploreRoutes(for city: String, count: Int = 2) async throws -> [ExploreActivity] {
        print("🚀 开始为\(city)生成\(count)条探索路线")

        isGenerating = true
        error = nil

        defer {
            isGenerating = false
        }

        do {
            // 1. 调用DeepSeek API获取路线推荐
            let routeRecommendations = try await fetchRouteRecommendations(city: city, count: count)
            print("✅ DeepSeek返回\(routeRecommendations.count)条路线推荐")

            // 2. 为每条路线生成真实轨迹
            var activities: [ExploreActivity] = []
            for (index, recommendation) in routeRecommendations.enumerated() {
                print("📍 处理路线 \(index + 1): \(recommendation.name)")

                let activity = try await RouteGenerator.shared.generateRoute(
                    from: recommendation,
                    city: city
                )
                activities.append(activity)
            }

            print("🎉 成功生成\(activities.count)条完整路线")
            return activities

        } catch {
            print("❌ 路线生成失败: \(error)")
            self.error = error
            throw error
        }
    }

    /// 生成自定义路线
    func generateCustomExploreRoutes(
        startLocation: String,
        requirements: String,
        minDistance: Double = 5.0,
        maxDistance: Double = 25.0,
        count: Int = 2,
        onRetryProgress: RetryProgressCallback? = nil,
        onRetrySuccess: RetrySuccessCallback? = nil
    ) async throws -> [ExploreActivity] {
        print("🎯 开始生成自定义路线: \(startLocation) -> \(requirements)")

        isGenerating = true
        error = nil

        defer {
            isGenerating = false
        }

        do {
            // 1. 调用DeepSeek API获取自定义路线推荐
            let routeRecommendations = try await fetchCustomRouteRecommendations(
                startLocation: startLocation,
                requirements: requirements,
                minDistance: minDistance,
                maxDistance: maxDistance,
                count: count,
                onRetryProgress: onRetryProgress,
                onRetrySuccess: onRetrySuccess
            )
            print("✅ DeepSeek返回\(routeRecommendations.count)条自定义路线推荐")

            // 2. 为每条路线生成真实轨迹
            var activities: [ExploreActivity] = []
            for (index, recommendation) in routeRecommendations.enumerated() {
                print("📍 处理自定义路线 \(index + 1): \(recommendation.name)")

                let activity = try await RouteGenerator.shared.generateRoute(
                    from: recommendation,
                    city: startLocation
                )
                activities.append(activity)
            }

            print("🎉 成功生成\(activities.count)条自定义路线")
            return activities

        } catch {
            print("❌ 自定义路线生成失败: \(error)")
            self.error = error
            throw error
        }
    }

    // MARK: - 私有方法

    /// 获取路线推荐
    private func fetchRouteRecommendations(city: String, count: Int) async throws -> [RouteRecommendation] {
        let prompt = createCityRoutePrompt(city: city, count: count)
        let response = try await callDeepSeekAPI(prompt: prompt)
        return try parseRouteRecommendations(from: response)
    }

    /// 获取自定义路线推荐（带智能重试机制）
    private func fetchCustomRouteRecommendations(
        startLocation: String,
        requirements: String,
        minDistance: Double,
        maxDistance: Double,
        count: Int,
        onRetryProgress: RetryProgressCallback? = nil,
        onRetrySuccess: RetrySuccessCallback? = nil
    ) async throws -> [RouteRecommendation] {
        return try await fetchCustomRouteRecommendationsWithRetry(
            startLocation: startLocation,
            requirements: requirements,
            minDistance: minDistance,
            maxDistance: maxDistance,
            count: count,
            onRetryProgress: onRetryProgress,
            onRetrySuccess: onRetrySuccess
        )
    }

    /// 带智能重试的自定义路线推荐获取
    private func fetchCustomRouteRecommendationsWithRetry(
        startLocation: String,
        requirements: String,
        minDistance: Double,
        maxDistance: Double,
        count: Int,
        maxDuration: TimeInterval = 300, // 5分钟
        onRetryProgress: RetryProgressCallback? = nil,
        onRetrySuccess: RetrySuccessCallback? = nil
    ) async throws -> [RouteRecommendation] {
        let startTime = Date()
        let maxAttempts = 10
        var lastError: Error?
        var lastResponse: String = ""

        let originalPrompt = createCustomRoutePrompt(
            startLocation: startLocation,
            requirements: requirements,
            minDistance: minDistance,
            maxDistance: maxDistance,
            count: count
        )

        for attempt in 1...maxAttempts {
            // 检查是否超过5分钟
            if Date().timeIntervalSince(startTime) > maxDuration {
                print("⏰ 智能重试超时（5分钟），停止尝试")
                break
            }

            print("🔄 第\(attempt)次尝试生成自定义路线...")

            // 如果是重试（第2次及以后），调用进度回调
            if attempt > 1 {
                let errorAnalysis = analyzeError(lastError!, response: lastResponse)
                await MainActor.run {
                    onRetryProgress?(attempt, errorAnalysis)
                }
            }

            do {
                let prompt: String
                if attempt == 1 {
                    prompt = originalPrompt
                } else {
                    // 生成包含错误反馈的提示词
                    let errorAnalysis = analyzeError(lastError!, response: lastResponse)
                    prompt = createErrorFeedbackPrompt(
                        originalPrompt: originalPrompt,
                        errorAnalysis: errorAnalysis,
                        attemptNumber: attempt
                    )
                    print("🔧 错误分析: \(errorAnalysis)")
                }

                let response = try await callDeepSeekAPI(prompt: prompt)
                let routes = try parseRouteRecommendations(from: response)

                print("✅ 第\(attempt)次尝试成功！生成了\(routes.count)条自定义路线")

                // 如果是重试成功，发送成功通知
                if attempt > 1 {
                    await MainActor.run {
                        NotificationCenter.default.post(
                            name: NSNotification.Name("RouteGenerationRetrySuccess"),
                            object: attempt
                        )
                    }
                }

                return routes

            } catch {
                lastError = error
                lastResponse = ""

                // 尝试获取响应内容用于分析
                if let apiError = error as? APIError,
                   case .parseError(let message) = apiError,
                   message.contains("原始响应:") {
                    let components = message.components(separatedBy: "原始响应:")
                    if components.count > 1 {
                        lastResponse = components[1].trimmingCharacters(in: .whitespacesAndNewlines)
                    }
                }

                print("❌ 第\(attempt)次尝试失败: \(error.localizedDescription)")

                // 如果是第一次失败，立即发送重试通知
                if attempt == 1 {
                    let errorAnalysis = analyzeError(error, response: lastResponse)
                    await MainActor.run {
                        NotificationCenter.default.post(
                            name: NSNotification.Name("RouteGenerationRetryAttempt"),
                            object: [
                                "attempt": 2, // 下一次是第2次尝试
                                "reason": errorAnalysis,
                                "remainingTime": maxDuration - Date().timeIntervalSince(startTime)
                            ]
                        )
                    }
                }

                // 如果是最后一次尝试，抛出错误
                if attempt == maxAttempts {
                    print("🚫 已达到最大重试次数(\(maxAttempts))，停止尝试")
                    throw error
                }

                // 短暂延迟后继续
                try await Task.sleep(nanoseconds: 1_000_000_000) // 1秒
            }
        }

        // 如果因为超时退出循环，抛出最后的错误
        throw lastError ?? APIError.parseError("重试超时")
    }

    /// 调用DeepSeek API
    private func callDeepSeekAPI(prompt: String) async throws -> String {
        guard let url = URL(string: baseURL) else {
            throw APIError.invalidURL
        }

        let request = ChatCompletionRequest(
            model: "deepseek-chat",
            messages: [
                ChatMessage(role: "system", content: "你是专业的骑行路线规划师，返回包含详细途经点介绍的JSON格式路线信息。"),
                ChatMessage(role: "user", content: prompt)
            ],
            temperature: 0.8,
            max_tokens: 2000,
            stream: false
        )

        var urlRequest = URLRequest(url: url)
        urlRequest.httpMethod = "POST"
        urlRequest.setValue("application/json", forHTTPHeaderField: "Content-Type")
        urlRequest.setValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")
        urlRequest.timeoutInterval = 180.0 // 设置3分钟超时
        urlRequest.httpBody = try JSONEncoder().encode(request)

        // 使用带超时的URLSession配置
        let config = URLSessionConfiguration.default
        config.timeoutIntervalForRequest = 180.0 // 3分钟请求超时
        config.timeoutIntervalForResource = 300.0 // 5分钟资源超时
        let session = URLSession(configuration: config)

        do {
            print("📡 第1步: 调用DeepSeek API生成路线推荐...")
            let (data, response) = try await session.data(for: urlRequest)

            guard let httpResponse = response as? HTTPURLResponse else {
                throw APIError.invalidResponse
            }

            guard httpResponse.statusCode == 200 else {
                let errorMessage = "HTTP \(httpResponse.statusCode)"
                print("❌ DeepSeek API错误: \(errorMessage)")
                throw APIError.serverError(httpResponse.statusCode, errorMessage)
            }

            let chatResponse = try JSONDecoder().decode(ChatCompletionResponse.self, from: data)
            guard let content = chatResponse.choices.first?.message.content else {
                throw APIError.invalidResponse
            }

            print("✅ DeepSeek API调用成功")
            return content

        } catch {
            if error is CancellationError {
                print("❌ DeepSeek API调用被取消")
                throw APIError.parseError("请求被取消")
            } else if (error as NSError).code == NSURLErrorTimedOut {
                print("❌ DeepSeek API调用超时")
                throw APIError.parseError("请求超时，请检查网络连接")
            } else {
                print("❌ DeepSeek API调用失败: \(error.localizedDescription)")
                throw error
            }
        }
    }

    /// 解析路线推荐
    private func parseRouteRecommendations(from response: String) throws -> [RouteRecommendation] {
        let jsonString = extractJSON(from: response)

        // 尝试多种编码方式
        var jsonData: Data?

        // 首先尝试UTF-8
        jsonData = jsonString.data(using: .utf8)

        // 如果UTF-8失败，尝试其他编码
        if jsonData == nil {
            jsonData = jsonString.data(using: .unicode)
        }

        if jsonData == nil {
            jsonData = jsonString.data(using: .ascii)
        }

        guard let data = jsonData else {
            print("❌ 无法转换JSON为数据，尝试的字符串: \(jsonString.prefix(200))...")
            throw APIError.parseError("无法转换为数据")
        }

        do {
            // 先验证JSON格式
            _ = try JSONSerialization.jsonObject(with: data, options: [])

            // 然后解码为结构体
            let routesResponse = try JSONDecoder().decode(RoutesResponse.self, from: data)
            return routesResponse.routes
        } catch {
            print("❌ JSON解析失败: \(error)")
            print("📄 原始响应: ```json\n\(response)\n```")
            print("🔧 提取的JSON: ```json\n\(jsonString)\n```")

            // 尝试修复常见的JSON问题
            let fixedJSON = fixCommonJSONIssues(jsonString)
            if let fixedData = fixedJSON.data(using: .utf8) {
                do {
                    let routesResponse = try JSONDecoder().decode(RoutesResponse.self, from: fixedData)
                    print("✅ JSON修复成功")
                    return routesResponse.routes
                } catch {
                    print("❌ JSON修复后仍然失败: \(error)")
                }
            }

            throw APIError.parseError("JSON解析失败: \(error.localizedDescription)")
        }
    }

    /// 从响应中提取JSON
    private func extractJSON(from content: String) -> String {
        var cleanContent = content
            .replacingOccurrences(of: "```json", with: "")
            .replacingOccurrences(of: "```", with: "")
            .trimmingCharacters(in: .whitespacesAndNewlines)

        // 移除可能的BOM和其他不可见字符
        cleanContent = cleanContent.replacingOccurrences(of: "\u{FEFF}", with: "") // BOM
        cleanContent = cleanContent.replacingOccurrences(of: "\u{200B}", with: "") // 零宽空格
        cleanContent = cleanContent.replacingOccurrences(of: "\u{00A0}", with: " ") // 不间断空格

        // 查找JSON开始和结束
        if let startIndex = cleanContent.firstIndex(of: "{"),
           let endIndex = cleanContent.lastIndex(of: "}") {
            cleanContent = String(cleanContent[startIndex...endIndex])
        }

        return cleanContent
    }

    /// 修复常见的JSON问题
    private func fixCommonJSONIssues(_ jsonString: String) -> String {
        var fixed = jsonString

        // 移除控制字符
        fixed = fixed.components(separatedBy: .controlCharacters).joined()

        // 修复可能的编码问题
        fixed = fixed.replacingOccurrences(of: "â€™", with: "'") // 智能引号编码问题
        fixed = fixed.replacingOccurrences(of: "â€œ", with: "\"") // 左双引号编码问题
        fixed = fixed.replacingOccurrences(of: "â€", with: "\"") // 右双引号编码问题
        fixed = fixed.replacingOccurrences(of: "ç", with: "c") // 特殊字符替换
        fixed = fixed.replacingOccurrences(of: "é", with: "e")
        fixed = fixed.replacingOccurrences(of: "à", with: "a")

        // 确保字符串是有效的UTF-8
        if let data = fixed.data(using: .utf8),
           let validString = String(data: data, encoding: .utf8) {
            return validString
        }

        return fixed
    }

    /// 创建城市路线prompt
    private func createCityRoutePrompt(city: String, count: Int) -> String {
        return """
        为\(city)生成\(count)条优质骑行路线。

        要求：
        1. 距离10-25公里
        2. 包含真实地点
        3. 每个waypoint都要有详细介绍

        JSON格式：
        {
          "routes": [
            {
              "name": "路线名称",
              "description": "路线特色和亮点描述",
              "distance": "15.5",
              "duration": "1-2小时",
              "difficulty": "简单",
              "highlights": ["景点1", "景点2", "景点3"],
              "roadCondition": "绿道/城市道路/混合路段",
              "bestTime": "最佳骑行时间段",
              "waypoints": [
                {
                  "name": "起点名称",
                  "address": "\(city)市具体详细地址",
                  "description": "详细介绍这个地点的特色、历史背景、适合骑行的原因等",
                  "type": "起点"
                },
                {
                  "name": "途经景点名称",
                  "address": "完整详细地址",
                  "description": "详细介绍景点特色、文化价值、游览建议、拍照点等",
                  "type": "景点"
                },
                {
                  "name": "休息点名称",
                  "address": "详细地址",
                  "description": "介绍休息点设施、周边环境、补给情况等",
                  "type": "途经点"
                },
                {
                  "name": "终点名称",
                  "address": "终点详细地址",
                  "description": "终点特色、结束后的活动建议、交通便利性等",
                  "type": "终点"
                }
              ]
            }
          ]
        }
        """
    }

    /// 创建自定义路线prompt
    private func createCustomRoutePrompt(
        startLocation: String,
        requirements: String,
        minDistance: Double,
        maxDistance: Double,
        count: Int
    ) -> String {
        let cityName = extractCityFromLocation(startLocation)

        return """
        **重要要求：严格按照用户需求生成骑行路线**

        起点：\(startLocation)
        用户需求：\(requirements)
        **距离限制：必须在\(Int(minDistance))-\(Int(maxDistance))公里范围内，不能超出！**

        **生成规则：**
        1. 路线总距离必须严格控制在\(Int(minDistance))-\(Int(maxDistance))公里内
        2. 必须根据用户需求"\(requirements)"来选择途径点和目的地
        3. 如果用户提到"咖啡"，必须包含咖啡厅作为途径点
        4. 如果用户提到"美食"，必须包含餐厅或美食街
        5. 如果用户提到"风景"，必须包含公园或景观点
        6. 路线要实用且符合骑行习惯

        生成\(count)条符合要求的个性化骑行路线：

        JSON格式：
        {
          "routes": [
            {
              "name": "路线名称（体现用户需求）",
              "description": "详细说明如何满足用户需求'\(requirements)'的路线特色",
              "distance": "实际距离（\(Int(minDistance))-\(Int(maxDistance))km范围内）",
              "duration": "预计骑行时间",
              "difficulty": "简单/中等/困难",
              "highlights": ["与用户需求相关的特色点1", "特色点2", "特色点3"],
              "roadCondition": "道路类型说明",
              "bestTime": "最佳骑行时间",
              "waypoints": [
                {
                  "name": "起始位置",
                  "address": "\(startLocation)",
                  "description": "起点特色和出发准备建议",
                  "type": "起点"
                },
                {
                  "name": "满足用户需求的目标地点（如咖啡厅/餐厅/景点）",
                  "address": "\(cityName)具体详细地址",
                  "description": "详细说明如何满足用户需求'\(requirements)'，包含具体的服务或特色",
                  "type": "景点"
                },
                {
                  "name": "中途休息点或补充需求点",
                  "address": "\(cityName)休息点详细地址",
                  "description": "休息设施、补给情况，如何进一步满足用户需求",
                  "type": "途经点"
                },
                {
                  "name": "终点位置",
                  "address": "\(cityName)终点详细地址",
                  "description": "终点特色、完成行程后的建议活动",
                  "type": "终点"
                }
              ]
            }
          ]
        }

        **再次提醒：距离必须在\(Int(minDistance))-\(Int(maxDistance))公里内，必须包含满足用户需求"\(requirements)"的地点！**
        """
    }

    /// 从位置字符串中提取城市名称
    private func extractCityFromLocation(_ location: String) -> String {
        let components = location.components(separatedBy: CharacterSet(charactersIn: "市区县"))
        if let firstComponent = components.first, firstComponent.count > 1 {
            return firstComponent + "市"
        }
        return location
    }

    // MARK: - 智能重试相关方法

    /// 分析错误并生成修复建议
    private func analyzeError(_ error: Error, response: String) -> String {
        let errorDescription = error.localizedDescription

        // JSON语法错误
        if errorDescription.contains("JSON") || errorDescription.contains("syntax") || errorDescription.contains("格式") {
            if errorDescription.contains("Unexpected character") {
                return "JSON中包含意外字符，请确保所有字符串都用双引号包围，避免使用特殊字符"
            } else if errorDescription.contains("bracket") || errorDescription.contains("括号") {
                return "JSON括号不匹配，请检查所有的大括号{}和方括号[]是否正确配对"
            } else {
                return "JSON格式错误，请严格按照标准JSON格式输出，确保语法正确"
            }
        }

        // 字段缺失错误
        if errorDescription.contains("keyNotFound") || errorDescription.contains("缺少") {
            return "JSON中缺少必需字段，请确保包含所有必需字段：name, description, distance, duration, difficulty, highlights, roadCondition, bestTime, waypoints"
        }

        // 数据类型错误
        if errorDescription.contains("typeMismatch") || errorDescription.contains("类型") {
            return "字段数据类型错误，请确保distance是字符串格式（如\"15.5\"），highlights是字符串数组，waypoints是对象数组"
        }

        // 编码错误
        if errorDescription.contains("encoding") || errorDescription.contains("编码") || errorDescription.contains("character") {
            return "字符编码问题，请避免使用特殊字符，使用标准的中文和英文字符"
        }

        // 数据验证错误
        if errorDescription.contains("距离") || errorDescription.contains("范围") {
            return "距离数据不符合要求，请确保距离在指定范围内，并且是合理的数值"
        }

        // 内容相关错误
        if errorDescription.contains("需求") || errorDescription.contains("要求") {
            return "未能满足用户需求，请确保路线包含用户要求的特定地点类型（如咖啡厅、餐厅、景点等）"
        }

        // 通用错误
        return "解析失败，请检查JSON格式是否正确，确保所有字段都存在且类型正确"
    }

    /// 创建包含错误反馈的提示词
    private func createErrorFeedbackPrompt(
        originalPrompt: String,
        errorAnalysis: String,
        attemptNumber: Int
    ) -> String {
        return """
        **这是第\(attemptNumber)次尝试，之前的尝试失败了**

        **错误原因：\(errorAnalysis)**

        **重要修复要求：**
        1. 严格按照JSON格式输出，不要有任何语法错误
        2. 确保所有字符串都用双引号包围
        3. 避免使用特殊字符，如ç、é、à等
        4. 确保所有必需字段都存在
        5. 距离必须是字符串格式，如"15.5"
        6. highlights必须是字符串数组
        7. waypoints必须是对象数组，每个对象包含name、address、description、type字段

        **请根据以下原始要求重新生成，并特别注意修复上述错误：**

        \(originalPrompt)

        **再次强调：**
        - 输出必须是完全有效的JSON格式
        - 不要包含任何markdown标记
        - 确保所有字段类型正确
        - 避免特殊字符和编码问题
        """
    }
}