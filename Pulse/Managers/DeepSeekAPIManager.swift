import Foundation
import Combine
import CoreLocation

// MARK: - DeepSeek API Manager
class DeepSeekAPIManager: ObservableObject {
    static let shared = DeepSeekAPIManager()
    
    private let apiKey = "sk-eefc087f46024597bc2bf5837a9cba49"
    private let baseURL = "https://api.deepseek.com/chat/completions"
    
    @Published var isGenerating = false
    @Published var error: Error?
    
    private init() {}
    
    func generateExploreRoutes(for city: String, count: Int = 2) async throws -> [ExploreActivity] {
        await MainActor.run {
            isGenerating = true
            error = nil
        }
        
        defer {
            Task { @MainActor in
                self.isGenerating = false
            }
        }
        
        do {
            let prompt = createRouteGenerationPrompt(for: city, count: count)
            
            let request = ChatCompletionRequest(
                model: "deepseek-chat",
                messages: [
                    ChatMessage(role: "system", content: "你是专业的骑行路线规划师，返回包含详细途经点介绍的JSON格式路线信息。"),
                    ChatMessage(role: "user", content: prompt)
                ],
                temperature: 0.9, // 增加随机性
                max_tokens: 1800,  // 减少token提高速度，2个路线够用
                stream: false
            )
            
            let response = try await sendRequest(request)
            return try await parseResponse(response, city: city)
        } catch {
            await MainActor.run {
                self.error = error
            }
            throw error
        }
    }
    
    // MARK: - 自定义路线生成
    func generateCustomExploreRoutes(startLocation: String, requirements: String, minDistance: Double = 5.0, maxDistance: Double = 25.0, count: Int = 2) async throws -> [ExploreActivity] {
        await MainActor.run {
            isGenerating = true
            error = nil
        }
        
        defer {
            Task { @MainActor in
                self.isGenerating = false
            }
        }
        
        do {
            let prompt = createCustomRouteGenerationPrompt(startLocation: startLocation, requirements: requirements, count: count, minDistance: minDistance, maxDistance: maxDistance)
            
            // 添加随机元素让每次生成都不同
            let randomElement = Int.random(in: 1...1000)
            let timestamp = Date().timeIntervalSince1970
            
            let request = ChatCompletionRequest(
                model: "deepseek-chat",
                messages: [
                    ChatMessage(role: "system", content: "你是专业的骑行路线规划师，根据用户需求和距离要求生成详细的JSON格式路线。"),
                    ChatMessage(role: "user", content: prompt),
                    ChatMessage(role: "assistant", content: "我理解您的需求，我会在\(Int(minDistance))-\(Int(maxDistance))公里范围内，根据不同的时间和随机因素(\(randomElement)-\(Int(timestamp)))为您生成多样化的路线方案，每个地点都会有详细的介绍。")
                ],
                temperature: 0.7, // 降低随机性，减少差异
                max_tokens: 1800,  // 减少token提高速度，2个路线够用
                stream: false
            )
            
            let response = try await sendRequest(request)
            return try await parseResponse(response, city: extractCityFromLocation(startLocation))
        } catch {
            await MainActor.run {
                self.error = error
            }
            throw error
        }
    }
    
    private func createRouteGenerationPrompt(for city: String, count: Int) -> String {
        // 添加时间和随机因素增加多样性
        let timeOfDay = Calendar.current.component(.hour, from: Date())
        let randomTheme = ["文化历史", "自然风光", "现代都市", "休闲娱乐", "美食探索"].randomElement() ?? "综合体验"
        
        return """
        为\(city)生成\(count)条优质骑行路线，主题：\(randomTheme)，时间：\(timeOfDay)点。
        
        要求：10-25公里，包含真实地点，每个waypoint都要有详细介绍。
        
        JSON格式：
        {
          "routes": [
            {
              "name": "路线名称",
              "description": "路线特色和亮点描述",
              "estimatedDistance": "15.5",
              "duration": "1-2小时",
              "difficulty": "简单",
              "highlights": ["景点1", "景点2", "景点3"],
              "roadCondition": "绿道/城市道路/混合路段",
              "bestTime": "最佳骑行时间段",
              "waypoints": [
                {
                  "name": "起点名称",
                  "address": "\(city)市具体详细地址",
                  "description": "详细介绍这个地点的特色、历史背景、适合骑行的原因等（50字以上）",
                  "type": "起点"
                },
                {
                  "name": "途经景点名称",
                  "address": "完整详细地址",
                  "description": "详细介绍景点特色、文化价值、游览建议、拍照点等（50字以上）",
                  "type": "景点"
                },
                {
                  "name": "休息点名称",
                  "address": "详细地址",
                  "description": "介绍休息点设施、周边环境、补给情况等（30字以上）",
                  "type": "途经点"
                },
                {
                  "name": "终点名称",
                  "address": "终点详细地址",
                  "description": "终点特色、结束后的活动建议、交通便利性等（40字以上）",
                  "type": "终点"
                }
              ],
              "routeInstructions": "详细的骑行路线指引和注意事项"
            }
          ]
        }
        
        重要：每个waypoint的description必须详细且有用，包含地点特色、历史文化、实用建议等信息。
        """
    }
    
    // MARK: - 自定义路线生成Prompt
    private func createCustomRouteGenerationPrompt(startLocation: String, requirements: String, count: Int, minDistance: Double = 5.0, maxDistance: Double = 25.0) -> String {
        // 添加随机因素和时间戳确保每次都不同
        let timeOfDay = Calendar.current.component(.hour, from: Date())
        let randomSeed = Int.random(in: 1000...9999)
        
        // 从起始位置提取城市信息
        let cityName = extractCityFromLocation(startLocation)
        
        return """
        基于用户需求生成\(count)条个性化骑行路线。
        
        起点：\(startLocation)
        用户需求：\(requirements)
        距离范围：\(Int(minDistance))-\(Int(maxDistance))公里
        时间：\(timeOfDay)点，种子：\(randomSeed)
        
        **重要约束条件：**
        1. 所有waypoints必须位于\(cityName)市内，绝对不能包含其他城市的地点
        2. 路线总距离必须控制在\(Int(minDistance))-\(Int(maxDistance))公里范围内
        3. 所有地址都必须包含\(cityName)市的具体区域信息
        
        JSON格式：
        {
          "routes": [
            {
              "name": "路线名称",
              "description": "路线特色和如何满足用户需求的描述",
              "estimatedDistance": "15.5",
              "duration": "1-1.5小时",
              "difficulty": "中等",
              "highlights": ["特色点1", "特色点2", "特色点3"],
              "roadCondition": "道路类型详细说明",
              "bestTime": "最佳骑行时间",
              "waypoints": [
                {
                  "name": "起始位置",
                  "address": "\(startLocation)",
                  "description": "起点特色和出发准备建议（30字以上）",
                  "type": "起点"
                },
                {
                  "name": "目标景点",
                  "address": "\(cityName)市XX区具体详细地址",
                  "description": "详细介绍如何满足用户需求、景点特色、游览建议等（50字以上）",
                  "type": "景点"
                },
                {
                  "name": "中途休息点",
                  "address": "\(cityName)市XX区休息点地址",
                  "description": "休息设施、补给情况、周边环境介绍（30字以上）",
                  "type": "途经点"
                },
                {
                  "name": "终点位置",
                  "address": "\(cityName)市XX区终点详细地址",
                  "description": "终点特色、完成行程后的建议活动（40字以上）",
                  "type": "终点"
                }
              ],
              "routeInstructions": "详细路线指引和注意事项"
            }
          ]
        }
        
        重要：
        1. 确保所有waypoints都在\(cityName)市内，不能有其他城市的地点
        2. 路线距离必须在指定范围内
        3. 每个waypoint都有详细实用的描述信息
        4. 地址必须包含完整的市区信息
        """
    }
    
    // MARK: - 辅助方法
    private func extractCityFromLocation(_ location: String) -> String {
        // 从位置字符串中提取城市名称
        let components = location.components(separatedBy: CharacterSet(charactersIn: "市区县"))
        if let firstComponent = components.first, firstComponent.count > 1 {
            return firstComponent + "市"
        }
        
        // 如果无法提取，返回原始位置
        return location
    }
    
    // 城市信息结构
    private struct CityInfo {
        let latitude: Double
        let longitude: Double
        let maxRadius: Double  // 城市最大半径（度为单位）
        let name: String
    }
    
    // 获取城市信息
    private func getCityInfo(_ city: String) -> CityInfo {
        if city.contains("成都") {
            return CityInfo(latitude: 30.6532, longitude: 104.0665, maxRadius: 0.12, name: "成都")
        } else if city.contains("北京") {
            return CityInfo(latitude: 39.9042, longitude: 116.4074, maxRadius: 0.20, name: "北京")
        } else if city.contains("上海") {
            return CityInfo(latitude: 31.2304, longitude: 121.4737, maxRadius: 0.18, name: "上海")
        } else if city.contains("广州") {
            return CityInfo(latitude: 23.1291, longitude: 113.2644, maxRadius: 0.15, name: "广州")
        } else if city.contains("深圳") {
            return CityInfo(latitude: 22.5431, longitude: 114.0579, maxRadius: 0.12, name: "深圳")
        } else if city.contains("杭州") {
            return CityInfo(latitude: 30.2741, longitude: 120.1551, maxRadius: 0.12, name: "杭州")
        } else if city.contains("南京") {
            return CityInfo(latitude: 32.0603, longitude: 118.7969, maxRadius: 0.12, name: "南京")
        } else if city.contains("武汉") {
            return CityInfo(latitude: 30.5928, longitude: 114.3055, maxRadius: 0.15, name: "武汉")
        } else if city.contains("西安") {
            return CityInfo(latitude: 34.2658, longitude: 108.9534, maxRadius: 0.12, name: "西安")
        } else if city.contains("苏州") {
            return CityInfo(latitude: 31.2989, longitude: 120.5853, maxRadius: 0.10, name: "苏州")
        } else {
            // 默认使用成都坐标
            return CityInfo(latitude: 30.6532, longitude: 104.0665, maxRadius: 0.12, name: "成都")
        }
    }
    
    private func sendRequest(_ request: ChatCompletionRequest) async throws -> ChatCompletionResponse {
        guard let url = URL(string: baseURL) else {
            throw APIError.invalidURL
        }
        
        var urlRequest = URLRequest(url: url)
        urlRequest.httpMethod = "POST"
        urlRequest.addValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")
        urlRequest.addValue("application/json", forHTTPHeaderField: "Content-Type")
        
        // 设置更长的超时时间，因为AI生成可能需要较长时间
        urlRequest.timeoutInterval = 300.0  // 延长到5分钟超时
        
        let encoder = JSONEncoder()
        let requestData = try encoder.encode(request)
        urlRequest.httpBody = requestData  // 设置请求体
        
        print("🚀 发送DeepSeek API请求，超时设置: \(urlRequest.timeoutInterval)秒")
        print("📊 请求体大小: \(requestData.count) bytes")
        
        // 记录请求时间戳用于去重
        let requestTimestamp = Date().timeIntervalSince1970
        print("⏰ 请求时间戳: \(requestTimestamp)")
        
        // 重试机制：最多重试1次（减少重复调用）
        var lastError: Error?
        for attempt in 1...1 {
            do {
                print("📡 尝试第\(attempt)次请求...")
                let (data, response) = try await URLSession.shared.data(for: urlRequest)
                
                guard let httpResponse = response as? HTTPURLResponse else {
                    throw APIError.invalidResponse
                }
                
                if httpResponse.statusCode != 200 {
                    let errorMessage = String(data: data, encoding: .utf8) ?? "Unknown error"
                    print("❌ HTTP错误 \(httpResponse.statusCode): \(errorMessage)")
                    throw APIError.serverError(httpResponse.statusCode, errorMessage)
                }
                
                print("✅ 请求成功，响应大小: \(data.count) bytes")
                let decoder = JSONDecoder()
                return try decoder.decode(ChatCompletionResponse.self, from: data)
                
            } catch {
                lastError = error
                print("⚠️ 第\(attempt)次请求失败: \(error.localizedDescription)")
                // 只进行1次尝试，不需要重试等待逻辑
            }
        }
        
        // 所有重试都失败了，抛出最后一个错误
        throw lastError ?? APIError.invalidResponse
    }
    
    private func parseResponse(_ response: ChatCompletionResponse, city: String) async throws -> [ExploreActivity] {
        guard let content = response.choices.first?.message.content else {
            throw APIError.invalidResponse
        }
        
        print("🤖 DeepSeek API 响应内容:")
        print(content)
        
        // 尝试提取JSON部分
        let jsonString = extractJSONFromContent(content)
        print("🔍 准备解析的JSON:")
        print(jsonString)
        
        guard let jsonData = jsonString.data(using: .utf8) else {
            print("❌ 无法将字符串转换为UTF-8数据")
            throw APIError.parseError("无法解析响应内容：字符编码问题")
        }
        
        // 验证JSON格式是否有效
        do {
            let testJSON = try JSONSerialization.jsonObject(with: jsonData, options: [])
            print("✅ JSON格式验证通过，类型: \(type(of: testJSON))")
        } catch {
            print("❌ JSON格式验证失败: \(error)")
            
            // 尝试找到具体的错误位置
            if let nsError = error as NSError?,
               let errorIndex = nsError.userInfo["NSJSONSerializationErrorIndex"] as? Int {
                print("🎯 错误位置: 字符索引 \(errorIndex)")
                if errorIndex < jsonString.count {
                    let errorStart = max(0, errorIndex - 50)
                    let errorEnd = min(jsonString.count, errorIndex + 50)
                    let startIndex = jsonString.index(jsonString.startIndex, offsetBy: errorStart)
                    let endIndex = jsonString.index(jsonString.startIndex, offsetBy: errorEnd)
                    let context = String(jsonString[startIndex..<endIndex])
                    print("🎯 错误上下文: \(context)")
                }
            }
            
            // 尝试修复常见的JSON格式问题
            let fixedJSON = fixCommonJSONIssues(jsonString)
            guard let fixedData = fixedJSON.data(using: .utf8) else {
                throw APIError.parseError("JSON格式修复失败")
            }
            
            do {
                let testFixed = try JSONSerialization.jsonObject(with: fixedData, options: [])
                print("✅ JSON修复成功，类型: \(type(of: testFixed))")
                // 使用修复后的数据
                guard let fixedJsonData = fixedJSON.data(using: .utf8) else {
                    throw APIError.parseError("修复后的JSON数据转换失败")
                }
                let routesResponse = try JSONDecoder().decode(RoutesResponse.self, from: fixedJsonData)
                print("✅ 成功解析 \(routesResponse.routes.count) 条路线")
                return try await processRoutes(routesResponse.routes, city: city)
            } catch {
                print("❌ JSON修复后仍然解析失败: \(error)")
                print("🔍 修复后的JSON前500个字符:")
                print(String(fixedJSON.prefix(500)))
                if fixedJSON.count > 500 {
                    print("🔍 修复后的JSON后100个字符:")
                    print(String(fixedJSON.suffix(100)))
                }
                
                // 尝试找到具体的错误位置
                if let nsError = error as NSError?,
                   let errorIndex = nsError.userInfo["NSJSONSerializationErrorIndex"] as? Int {
                    print("🎯 错误位置: 字符索引 \(errorIndex)")
                    if errorIndex < fixedJSON.count {
                        let errorStart = max(0, errorIndex - 50)
                        let errorEnd = min(fixedJSON.count, errorIndex + 50)
                        let startIndex = fixedJSON.index(fixedJSON.startIndex, offsetBy: errorStart)
                        let endIndex = fixedJSON.index(fixedJSON.startIndex, offsetBy: errorEnd)
                        let context = String(fixedJSON[startIndex..<endIndex])
                        print("🎯 错误上下文: \(context)")
                    }
                }
                
                throw APIError.parseError("JSON解析失败: \(error.localizedDescription)")
            }
        }
        
        do {
            let routesResponse = try JSONDecoder().decode(RoutesResponse.self, from: jsonData)
            print("✅ 成功解析 \(routesResponse.routes.count) 条路线")
            return try await processRoutes(routesResponse.routes, city: city)
        } catch {
            print("❌ JSON解析失败: \(error)")
            throw APIError.parseError("JSON解析失败: \(error.localizedDescription)")
        }
    }
    
    // MARK: - JSON修复辅助函数（增强版）
    private func fixCommonJSONIssues(_ jsonString: String) -> String {
        var fixed = jsonString
        
        print("🔧 开始JSON修复，原始长度: \\(fixed.count)")
        
        // 步骤1：预处理 - 修复常见的编码问题
        fixed = preProcessJSONString(fixed)
        
        // 步骤2：修复引号问题
        fixed = fixQuotationMarks(fixed)
        
        // 步骤3：修复特殊字符
        fixed = fixSpecialCharacters(fixed)
        
        // 步骤4：修复JSON结构问题
        fixed = fixJSONStructure(fixed)
        
        // 步骤5：平衡括号
        fixed = balanceJSONBrackets(fixed)
        
        // 步骤6：最终验证和清理
        fixed = finalizeJSONString(fixed)
        
        print("🔧 JSON修复完成，最终长度: \\(fixed.count)")
        return fixed
    }
    
    // 预处理JSON字符串
    private func preProcessJSONString(_ jsonString: String) -> String {
        var result = jsonString
        
        // 移除BOM标记
        if result.hasPrefix("\\u{FEFF}") {
            result = String(result.dropFirst())
        }
        
        // 修复常见的Unicode问题
        result = result
            .replacingOccurrences(of: "\\u00a0", with: " ")  // 不间断空格
            .replacingOccurrences(of: "\\u2028", with: "\\\\n")  // 行分隔符
            .replacingOccurrences(of: "\\u2029", with: "\\\\n")  // 段落分隔符
        
        return result
    }
    
    // 修复引号问题
    private func fixQuotationMarks(_ jsonString: String) -> String {
        return jsonString
            .replacingOccurrences(of: "\u{201C}", with: "\"")  // 中文左双引号
            .replacingOccurrences(of: "\u{201D}", with: "\"")  // 中文右双引号
            .replacingOccurrences(of: "\u{2018}", with: "'")   // 中文左单引号
            .replacingOccurrences(of: "\u{2019}", with: "'")   // 中文右单引号
            .replacingOccurrences(of: "\u{201E}", with: "\"")  // 德语引号
            .replacingOccurrences(of: "\u{201A}", with: "'")   // 德语单引号
    }
    
    // 修复特殊字符
    private func fixSpecialCharacters(_ jsonString: String) -> String {
        return jsonString
            .replacingOccurrences(of: "…", with: "...")  // 省略号
            .replacingOccurrences(of: "—", with: "-")   // 长破折号
            .replacingOccurrences(of: "–", with: "-")   // 短破折号
            .replacingOccurrences(of: "→", with: "->")  // 箭头
            .replacingOccurrences(of: "←", with: "<-")  // 左箭头
            .replacingOccurrences(of: "↑", with: "^")   // 上箭头
            .replacingOccurrences(of: "↓", with: "v")   // 下箭头
            .replacingOccurrences(of: "•", with: "*")   // 项目符号
            .replacingOccurrences(of: "◆", with: "*")   // 钻石符号
            .replacingOccurrences(of: "★", with: "*")   // 星号
            .replacingOccurrences(of: "℃", with: "°C")  // 摄氏度
            .replacingOccurrences(of: "℉", with: "°F")  // 华氏度
    }
    
    // 修复JSON结构问题
    private func fixJSONStructure(_ jsonString: String) -> String {
        var result = jsonString
        
        // 修复缺失的逗号
        result = addMissingCommas(result)
        
        // 修复错误的转义字符
        result = fixEscapeCharacters(result)
        
        // 修复多余的逗号
        result = removeExtraCommas(result)
        
        return result
    }
    
    // 添加缺失的逗号
    private func addMissingCommas(_ jsonString: String) -> String {
        // 在JSON对象属性之间添加缺失的逗号
        return jsonString.replacingOccurrences(
            of: "\"\\s*\\n\\s*\"", 
            with: "\",\\n    \"", 
            options: .regularExpression
        )
    }
    
    // 修复转义字符
    private func fixEscapeCharacters(_ jsonString: String) -> String {
        var result = jsonString
        
        // 修复反斜杠问题
        result = result.replacingOccurrences(of: "\\\\\\\\", with: "\\\\")
        
        // 修复换行符
        result = result.replacingOccurrences(of: "\\\\n", with: "\\\\n")
        result = result.replacingOccurrences(of: "\\\\r", with: "\\\\r")
        result = result.replacingOccurrences(of: "\\\\t", with: "\\\\t")
        
        return result
    }
    
    // 移除多余的逗号
    private func removeExtraCommas(_ jsonString: String) -> String {
        var result = jsonString
        
        // 移除对象或数组结束前的多余逗号
        result = result.replacingOccurrences(
            of: ",\\s*([}\\]])", 
            with: "$1", 
            options: .regularExpression
        )
        
        return result
    }
    
    // 最终化JSON字符串
    private func finalizeJSONString(_ jsonString: String) -> String {
        var result = jsonString.trimmingCharacters(in: .whitespacesAndNewlines)
        
        // 确保以有效的JSON字符开始和结束
        if !result.hasPrefix("{") && !result.hasPrefix("[") {
            if let firstBrace = result.firstIndex(of: "{") {
                result = String(result[firstBrace...])
            } else if let firstBracket = result.firstIndex(of: "[") {
                result = String(result[firstBracket...])
            }
        }
        
        return result
    }
    
    // MARK: - 路线处理函数
    private func processRoutes(_ routes: [RouteResponse], city: String) async throws -> [ExploreActivity] {
        print("🛠️ 开始处理 \(routes.count) 条路线，城市: \(city)")
        
        // 创建AppleMapDirectionsService实例来生成轨迹
        let directionsService = AppleMapDirectionsService.shared
        
        var activities: [ExploreActivity] = []
        
        for (index, route) in routes.enumerated() {
            print("📍 处理路线 \(index + 1)/\(routes.count): '\(route.name)'")
            print("  - 包含 \(route.waypoints.count) 个关键点")
            print("  - 预估距离: \(route.estimatedDistance)")
            
            // 输出途径点详情
            for (i, waypoint) in route.waypoints.enumerated() {
                print("    途径点 \(i + 1): \(waypoint.name) (\(waypoint.type)) - \(waypoint.address)")
            }
            
            // 将 SimpleWaypoint 转换为完整的 Waypoint（包含坐标）
            let fullWaypoints = convertToFullWaypoints(route.waypoints, city: city)

            // 尝试使用Apple Maps生成真实轨迹，带重试机制
            var trackPoints: [ExploreTrackPoint] = []
            var useAppleMaps = true

            for attempt in 1...3 { // 最多重试3次
                do {
                    print("🗺️ 尝试 \(attempt)/3: 使用Apple Maps生成真实骑行轨迹，城市: \(city)")

                    // 调用Apple Maps生成真实的骑行轨迹
                    trackPoints = try await directionsService.generateCyclingRoute(from: fullWaypoints, cityContext: city)

                    print("✅ Apple Maps成功生成轨迹，包含 \(trackPoints.count) 个轨迹点")
                    break // 成功，退出重试循环

                } catch {
                    print("⚠️ Apple Maps尝试 \(attempt)/3 失败: \(error.localizedDescription)")

                    if attempt == 3 {
                        // 所有重试都失败，使用fallback
                        print("🔄 Apple Maps重试失败，使用智能fallback算法...")
                        trackPoints = generateFallbackTrack(for: fullWaypoints, city: city)
                        useAppleMaps = false
                        print("🆘 Fallback轨迹生成完成，包含 \(trackPoints.count) 个轨迹点")
                    } else {
                        // 等待后重试
                        print("⏱️ 等待 \(attempt) 秒后重试...")
                        try await Task.sleep(nanoseconds: UInt64(attempt * 1_000_000_000))
                    }
                }
            }

            // 验证轨迹质量
            if trackPoints.isEmpty {
                print("⚠️ 生成的轨迹为空，使用最小fallback轨迹")
                trackPoints = generateMinimalFallbackTrack(for: fullWaypoints)
            }

            let distanceValue = extractDistanceValue(from: route.estimatedDistance)

            let activity = ExploreActivity(
                title: route.name,
                description: route.description,
                distance: distanceValue,
                duration: route.duration,
                difficulty: route.difficulty,
                highlights: route.highlights,
                roadCondition: route.roadCondition,
                bestTime: route.bestTime,
                waypoints: fullWaypoints, // 保持原始的途径点信息
                gpxTrack: trackPoints,
                city: city
            )

            activities.append(activity)

            print("📍 路线创建完成: \(route.name) (使用\(useAppleMaps ? "Apple Maps" : "Fallback")轨迹)")
        }
        
        print("🎯 路线处理完成，共生成 \(activities.count) 个活动")
        return activities
    }
    
    private func extractJSONFromContent(_ content: String) -> String {
        print("🔧 开始处理响应内容，长度: \(content.count)")
        
        // 清理基本的markdown格式和可能的前缀
        var cleanContent = content
            .replacingOccurrences(of: "```json", with: "")
            .replacingOccurrences(of: "```JSON", with: "")
            .replacingOccurrences(of: "```", with: "")
            .replacingOccurrences(of: "json:", with: "")
            .replacingOccurrences(of: "JSON:", with: "")
            .replacingOccurrences(of: "以下是推荐的路线：", with: "")
            .replacingOccurrences(of: "推荐路线如下：", with: "")
            .trimmingCharacters(in: .whitespacesAndNewlines)
        
        // 修复常见的编码问题 - 使用Unicode转义序列避免编译器问题
        cleanContent = cleanContent
            .replacingOccurrences(of: "ç", with: "")  // 移除有问题的字符
            .replacingOccurrences(of: "\u{201C}", with: "\\\"")  // 左双引号
            .replacingOccurrences(of: "\u{201D}", with: "\\\"")  // 右双引号
            .replacingOccurrences(of: "\u{2018}", with: "'")   // 左单引号
            .replacingOccurrences(of: "\u{2019}", with: "'")   // 右单引号
            .replacingOccurrences(of: "\u{201C}", with: "\\\"")   // 中文左双引号
            .replacingOccurrences(of: "\u{201D}", with: "\\\"")   // 中文右双引号
            .replacingOccurrences(of: "\u{2018}", with: "'")      // 中文左单引号
            .replacingOccurrences(of: "\u{2019}", with: "'")      // 中文右单引号
        
        // 检查内容是否为空
        guard !cleanContent.isEmpty else {
            print("⚠️ 清理后内容为空")
            return ""
        }
        
        // 寻找第一个 { 和最后一个 } 来提取JSON
        // 使用更安全的方法：计算大括号的匹配
        var jsonStart = -1
        var jsonEnd = -1
        var braceCount = 0
        var inString = false
        var escapeNext = false
        
        for (index, char) in cleanContent.enumerated() {
            if escapeNext {
                escapeNext = false
                continue
            }
            
            if char == "\\" {
                escapeNext = true
                continue
            }
            
            if char == "\"" {
                inString.toggle()
                continue
            }
            
            if !inString {
                if char == "{" {
                    if jsonStart == -1 {
                        jsonStart = index
                    }
                    braceCount += 1
                } else if char == "}" {
                    braceCount -= 1
                    if braceCount == 0 && jsonStart != -1 {
                        jsonEnd = index
                        break
                    }
                }
            }
        }
        
        // 如果找到了完整的JSON结构
        if jsonStart != -1 && jsonEnd != -1 && jsonStart < jsonEnd {
            let startIndex = cleanContent.index(cleanContent.startIndex, offsetBy: jsonStart)
            let endIndex = cleanContent.index(cleanContent.startIndex, offsetBy: jsonEnd + 1)
            let extractedJSON = String(cleanContent[startIndex..<endIndex])
            print("🔧 成功提取JSON，长度: \(extractedJSON.count)")
            return extractedJSON
        }
        
        // 如果没有找到完整结构，尝试验证整个内容是否为JSON
        if cleanContent.hasPrefix("{") && cleanContent.hasSuffix("}") {
            print("🔧 使用整个内容作为JSON")
            return cleanContent
        }
        
        print("⚠️ 无法提取有效的JSON结构")
        return cleanContent
    }
    
    private func extractDistanceValue(from distanceString: String) -> Double {
        // 从字符串中提取数字部分，如"18.2km" -> 18.2
        let numberString = distanceString.replacingOccurrences(of: "km", with: "")
            .replacingOccurrences(of: "公里", with: "")
            .trimmingCharacters(in: .whitespaces)

        return Double(numberString) ?? 10.0 // 默认值10公里
    }

    // 将 SimpleWaypoint 转换为完整的 Waypoint（包含坐标）
    private func convertToFullWaypoints(_ simpleWaypoints: [SimpleWaypoint], city: String) -> [Waypoint] {
        print("🔄 开始为 \(simpleWaypoints.count) 个地点进行地理编码...")

        // 使用同步的地理编码来获取真实坐标
        var waypoints: [Waypoint] = []
        let geocoder = CLGeocoder()
        let cityInfo = getCityInfo(city)

        // 使用DispatchGroup来等待所有地理编码完成
        let group = DispatchGroup()
        let queue = DispatchQueue.global(qos: .userInitiated)

        for (index, simpleWaypoint) in simpleWaypoints.enumerated() {
            group.enter()

            queue.async {
                // 构建更精确的查询字符串
                let queries = [
                    "\(simpleWaypoint.name), \(city)",
                    "\(simpleWaypoint.address), \(city)",
                    simpleWaypoint.address.contains(city) ? simpleWaypoint.address : "\(simpleWaypoint.address), \(city)"
                ].filter { !$0.isEmpty }

                var bestCoordinate: CLLocationCoordinate2D?
                let semaphore = DispatchSemaphore(value: 0)

                // 尝试多个查询策略
                for query in queries {
                    geocoder.geocodeAddressString(query) { placemarks, error in
                        defer { semaphore.signal() }

                        if let placemark = placemarks?.first,
                           let location = placemark.location {
                            let coordinate = location.coordinate

                            // 验证坐标是否在城市范围内
                            if self.isCoordinateInCityRange(coordinate, city: city) {
                                bestCoordinate = coordinate
                                print("✅ 地理编码成功: \(simpleWaypoint.name) -> (\(String(format: "%.6f", coordinate.latitude)), \(String(format: "%.6f", coordinate.longitude)))")
                                return
                            }
                        }

                        if error != nil {
                            print("⚠️ 地理编码失败: \(query) - \(error?.localizedDescription ?? "未知错误")")
                        }
                    }

                    semaphore.wait()

                    if bestCoordinate != nil {
                        break
                    }

                    // 避免频率限制
                    Thread.sleep(forTimeInterval: 0.5)
                }

                // 如果地理编码失败，使用智能的fallback坐标
                let finalCoordinate = bestCoordinate ?? self.generateSmartFallbackCoordinate(
                    for: simpleWaypoint,
                    index: index,
                    total: simpleWaypoints.count,
                    city: city
                )

                let waypoint = Waypoint(
                    name: simpleWaypoint.name,
                    address: simpleWaypoint.address,
                    description: simpleWaypoint.description,
                    type: simpleWaypoint.type,
                    latitude: finalCoordinate.latitude,
                    longitude: finalCoordinate.longitude
                )

                DispatchQueue.main.async {
                    waypoints.append(waypoint)
                    group.leave()
                }
            }
        }

        group.wait()

        // 按原始顺序排序
        waypoints.sort { waypoint1, waypoint2 in
            let index1 = simpleWaypoints.firstIndex { $0.name == waypoint1.name } ?? 0
            let index2 = simpleWaypoints.firstIndex { $0.name == waypoint2.name } ?? 0
            return index1 < index2
        }

        print("✅ 地理编码完成，成功处理 \(waypoints.count) 个地点")
        return waypoints
    }

    // 检查坐标是否在城市范围内
    private func isCoordinateInCityRange(_ coordinate: CLLocationCoordinate2D, city: String) -> Bool {
        let cityInfo = getCityInfo(city)
        let cityCenter = CLLocation(latitude: cityInfo.latitude, longitude: cityInfo.longitude)
        let coordinateLocation = CLLocation(latitude: coordinate.latitude, longitude: coordinate.longitude)
        let distance = cityCenter.distance(from: coordinateLocation) / 1000.0 // 转换为公里

        let maxDistance = cityInfo.maxRadius * 111.0 // 转换为公里（1度约111公里）
        return distance <= maxDistance
    }

    // 生成智能的fallback坐标
    private func generateSmartFallbackCoordinate(
        for waypoint: SimpleWaypoint,
        index: Int,
        total: Int,
        city: String
    ) -> CLLocationCoordinate2D {
        let cityInfo = getCityInfo(city)

        // 根据waypoint类型和名称生成更合理的位置
        var baseOffset: (lat: Double, lng: Double) = (0, 0)

        // 根据地点名称特征调整位置
        if waypoint.name.contains("公园") || waypoint.name.contains("湖") {
            // 公园通常在城市边缘
            baseOffset = (0.02, 0.02)
        } else if waypoint.name.contains("商场") || waypoint.name.contains("购物") {
            // 商场通常在市中心
            baseOffset = (0.005, 0.005)
        } else if waypoint.name.contains("路") || waypoint.name.contains("街") {
            // 道路分布较广
            baseOffset = (0.015, 0.015)
        }

        // 根据索引分布地点
        let angle = (Double(index) / Double(total)) * 2 * Double.pi
        let radius = 0.01 + Double(index % 3) * 0.01 // 0.01-0.03度范围

        let latitude = cityInfo.latitude + baseOffset.lat + radius * cos(angle)
        let longitude = cityInfo.longitude + baseOffset.lng + radius * sin(angle)

        print("🎲 为 \(waypoint.name) 生成智能fallback坐标: (\(String(format: "%.6f", latitude)), \(String(format: "%.6f", longitude)))")

        return CLLocationCoordinate2D(latitude: latitude, longitude: longitude)
    }
    
    // 生成备用轨迹（当Apple Maps失败时使用）
    private func generateFallbackTrack(for waypoints: [Waypoint], city: String) -> [ExploreTrackPoint] {
        print("🔄 为 \(waypoints.count) 个地点生成备用轨迹（保持原始地点信息）")

        // 使用原始waypoint的坐标，而不是重新生成
        var trackPoints: [ExploreTrackPoint] = []

        // 直接使用waypoint的坐标信息
        for i in 0..<waypoints.count {
            let waypoint = waypoints[i]

            // 添加waypoint本身
            let waypointTrackPoint = ExploreTrackPoint(
                latitude: waypoint.latitude,
                longitude: waypoint.longitude,
                elevation: 500.0 + Double.random(in: -20...30),
                name: waypoint.name
            )
            trackPoints.append(waypointTrackPoint)

            // 如果不是最后一个点，生成到下一个点的智能路径
            if i < waypoints.count - 1 {
                let nextWaypoint = waypoints[i + 1]
                let pathPoints = generateSmartPath(
                    from: waypoint,
                    to: nextWaypoint,
                    city: city
                )
                trackPoints.append(contentsOf: pathPoints)
            }
        }

        print("✅ 生成备用轨迹，包含 \(trackPoints.count) 个点，保持了原始地点: \(waypoints.map { $0.name }.joined(separator: ", "))")
        return trackPoints
    }
    
    // 生成两点之间的智能路径（模拟真实道路网络）
    private func generateSmartPath(
        from startWaypoint: Waypoint,
        to endWaypoint: Waypoint,
        city: String
    ) -> [ExploreTrackPoint] {
        var pathPoints: [ExploreTrackPoint] = []

        let startLat = startWaypoint.latitude
        let startLng = startWaypoint.longitude
        let endLat = endWaypoint.latitude
        let endLng = endWaypoint.longitude

        // 计算距离来决定路径复杂度
        let distance = sqrt(pow(endLat - startLat, 2) + pow(endLng - startLng, 2))
        let segmentCount = max(5, min(20, Int(distance * 1000))) // 根据距离调整点数

        // 生成模拟城市道路网格的路径
        let pathType = choosePathType(from: startWaypoint, to: endWaypoint)

        switch pathType {
        case .direct:
            // 直接路径（适用于距离较近的点）
            pathPoints = generateDirectPath(from: (startLat, startLng), to: (endLat, endLng), segments: segmentCount)

        case .manhattan:
            // 曼哈顿式路径（模拟城市网格）
            pathPoints = generateManhattanPath(from: (startLat, startLng), to: (endLat, endLng), segments: segmentCount)

        case .curved:
            // 弯曲路径（模拟环路或绕行）
            pathPoints = generateCurvedPath(from: (startLat, startLng), to: (endLat, endLng), segments: segmentCount)
        }

        return pathPoints
    }

    // 选择路径类型
    private func choosePathType(from start: Waypoint, to end: Waypoint) -> PathType {
        let distance = sqrt(pow(end.latitude - start.latitude, 2) + pow(end.longitude - start.longitude, 2))

        if distance < 0.01 {
            return .direct
        } else if distance < 0.03 {
            return .manhattan
        } else {
            return .curved
        }
    }

    // 生成直接路径
    private func generateDirectPath(from start: (Double, Double), to end: (Double, Double), segments: Int) -> [ExploreTrackPoint] {
        var points: [ExploreTrackPoint] = []

        for i in 1..<segments {
            let progress = Double(i) / Double(segments)

            let lat = start.0 + (end.0 - start.0) * progress
            let lng = start.1 + (end.1 - start.1) * progress

            // 添加小幅随机偏移
            let offsetLat = lat + Double.random(in: -0.0002...0.0002)
            let offsetLng = lng + Double.random(in: -0.0002...0.0002)

            let point = ExploreTrackPoint(
                latitude: offsetLat,
                longitude: offsetLng,
                elevation: 500.0 + Double.random(in: -10...10),
                name: "路径点"
            )
            points.append(point)
        }

        return points
    }

    // 生成曼哈顿式路径（模拟城市网格）
    private func generateManhattanPath(from start: (Double, Double), to end: (Double, Double), segments: Int) -> [ExploreTrackPoint] {
        var points: [ExploreTrackPoint] = []

        // 决定是先横向还是先纵向
        let horizontalFirst = Bool.random()
        let midPoint: (Double, Double)

        if horizontalFirst {
            midPoint = (start.0, end.1) // 先横向到目标经度，再纵向
        } else {
            midPoint = (end.0, start.1) // 先纵向到目标纬度，再横向
        }

        let halfSegments = segments / 2

        // 第一段路径
        for i in 1..<halfSegments {
            let progress = Double(i) / Double(halfSegments)
            let lat = start.0 + (midPoint.0 - start.0) * progress
            let lng = start.1 + (midPoint.1 - start.1) * progress

            let point = ExploreTrackPoint(
                latitude: lat + Double.random(in: -0.0001...0.0001),
                longitude: lng + Double.random(in: -0.0001...0.0001),
                elevation: 500.0 + Double.random(in: -5...5),
                name: "路径点"
            )
            points.append(point)
        }

        // 中间点
        let midTrackPoint = ExploreTrackPoint(
            latitude: midPoint.0,
            longitude: midPoint.1,
            elevation: 500.0,
            name: "转弯点"
        )
        points.append(midTrackPoint)

        // 第二段路径
        for i in 1..<(segments - halfSegments) {
            let progress = Double(i) / Double(segments - halfSegments)
            let lat = midPoint.0 + (end.0 - midPoint.0) * progress
            let lng = midPoint.1 + (end.1 - midPoint.1) * progress

            let point = ExploreTrackPoint(
                latitude: lat + Double.random(in: -0.0001...0.0001),
                longitude: lng + Double.random(in: -0.0001...0.0001),
                elevation: 500.0 + Double.random(in: -5...5),
                name: "路径点"
            )
            points.append(point)
        }

        return points
    }

    // 生成弯曲路径
    private func generateCurvedPath(from start: (Double, Double), to end: (Double, Double), segments: Int) -> [ExploreTrackPoint] {
        var points: [ExploreTrackPoint] = []

        // 生成控制点来创建贝塞尔曲线效果
        let midLat = (start.0 + end.0) / 2
        let midLng = (start.1 + end.1) / 2

        // 添加偏移来创建弯曲
        let perpAngle = atan2(end.1 - start.1, end.0 - start.0) + Double.pi / 2
        let curvature = 0.01 // 弯曲程度

        let controlLat = midLat + curvature * cos(perpAngle)
        let controlLng = midLng + curvature * sin(perpAngle)

        for i in 1..<segments {
            let t = Double(i) / Double(segments)

            // 二次贝塞尔曲线
            let lat = pow(1-t, 2) * start.0 + 2 * (1-t) * t * controlLat + pow(t, 2) * end.0
            let lng = pow(1-t, 2) * start.1 + 2 * (1-t) * t * controlLng + pow(t, 2) * end.1

            let point = ExploreTrackPoint(
                latitude: lat + Double.random(in: -0.0001...0.0001),
                longitude: lng + Double.random(in: -0.0001...0.0001),
                elevation: 500.0 + sin(t * Double.pi * 2) * 20 + Double.random(in: -10...10),
                name: "路径点"
            )
            points.append(point)
        }

        return points
    }

    // 生成最小fallback轨迹（紧急情况下使用）
    private func generateMinimalFallbackTrack(for waypoints: [Waypoint]) -> [ExploreTrackPoint] {
        print("🆘 生成最小fallback轨迹，包含 \(waypoints.count) 个基础点")

        return waypoints.map { waypoint in
            ExploreTrackPoint(
                latitude: waypoint.latitude,
                longitude: waypoint.longitude,
                elevation: 500.0,
                name: waypoint.name
            )
        }
    }

    // 路径类型枚举
    private enum PathType {
        case direct    // 直接路径
        case manhattan // 曼哈顿式路径
        case curved    // 弯曲路径
    }
    
    // 新增：平衡JSON括号的方法
    private func balanceJSONBrackets(_ jsonString: String) -> String {
        var result = jsonString
        var braceCount = 0
        var bracketCount = 0
        var inString = false
        var escapeNext = false
        
        // 计算当前的括号状态
        for char in result {
            if escapeNext {
                escapeNext = false
                continue
            }
            
            if char == "\\" {
                escapeNext = true
                continue
            }
            
            if char == "\"" {
                inString.toggle()
                continue
            }
            
            if !inString {
                switch char {
                case "{":
                    braceCount += 1
                case "}":
                    braceCount -= 1
                case "[":
                    bracketCount += 1
                case "]":
                    bracketCount -= 1
                default:
                    break
                }
            }
        }
        
        // 补齐缺失的闭合括号
        if braceCount > 0 {
            result += String(repeating: "}", count: braceCount)
            print("🔧 补齐了 \(braceCount) 个缺失的右大括号")
        }
        
        if bracketCount > 0 {
            result += String(repeating: "]", count: bracketCount)
            print("🔧 补齐了 \(bracketCount) 个缺失的右方括号")
        }
        
        return result
    }
}

// MARK: - API请求模型
struct ChatCompletionRequest: Codable {
    let model: String
    let messages: [ChatMessage]
    let temperature: Double
    let max_tokens: Int
    let stream: Bool
}

struct ChatMessage: Codable {
    let role: String
    let content: String
}

// MARK: - API响应模型
struct ChatCompletionResponse: Codable {
    let choices: [Choice]
    
    struct Choice: Codable {
        let message: ChatMessage
    }
}

struct RoutesResponse: Codable {
    let routes: [RouteResponse]
}

struct RouteResponse: Codable {
    let name: String
    let description: String
    let estimatedDistance: String
    let duration: String
    let difficulty: String
    let highlights: [String]
    let roadCondition: String
    let bestTime: String
    let waypoints: [SimpleWaypoint]
    let routeInstructions: String
}

// 旧的GeoJSON模型已移除，现在使用Waypoint模型

// MARK: - 错误类型
enum APIError: LocalizedError {
    case invalidURL
    case invalidResponse
    case serverError(Int, String)
    case parseError(String)
    
    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "无效的API地址"
        case .invalidResponse:
            return "无效的API响应"
        case .serverError(let code, let message):
            return "服务器错误 \(code): \(message)"
        case .parseError(let message):
            return "数据解析错误: \(message)"
        }
    }
} 