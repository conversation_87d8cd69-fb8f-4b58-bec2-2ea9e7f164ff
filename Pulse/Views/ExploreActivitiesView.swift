import SwiftUI
import CoreLocation
import MapKit

// MARK: - Mapbox错误类型
enum MapboxError: LocalizedError {
    case invalidURL
    case invalidResponse
    case serverError(Int, String)
    case invalidImageData
    case insufficientTrackPoints
    case invalidGeoJSON
    
    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "无效的Mapbox API地址"
        case .invalidResponse:
            return "无效的API响应"
        case .serverError(let code, let message):
            return "Mapbox服务器错误 \(code): \(message)"
        case .invalidImageData:
            return "无法解析图片数据"
        case .insufficientTrackPoints:
            return "轨迹点数量不足"
        case .invalidGeoJSON:
            return "GeoJSON格式错误"
        }
    }
}

// MARK: - 加载状态管理
class LoadingStepManager: ObservableObject {
    @Published var currentStep: LoadingStep = .analyzing
    @Published var completedSteps: Set<LoadingStep> = []
    @Published var progress: Double = 0.0
    
    enum LoadingStep: Int, CaseIterable {
        case analyzing = 0
        case planning = 1
        case generating = 2
        case snapshot = 3
        
        var title: String {
            switch self {
            case .analyzing: return "分析起始位置和需求"
            case .planning: return "AI智能路线规划"
            case .generating: return "生成精确轨迹"
            case .snapshot: return "制作地图快照"
            }
        }
        
        var icon: String {
            switch self {
            case .analyzing: return "location.fill"
            case .planning: return "brain.head.profile"
            case .generating: return "map.fill"
            case .snapshot: return "camera.fill"
            }
        }
    }
    
    func setStep(_ step: LoadingStep) {
        currentStep = step
        completedSteps.insert(step)
        progress = Double(step.rawValue + 1) / Double(LoadingStep.allCases.count)
    }
    
    func completeStep(_ step: LoadingStep) {
        completedSteps.insert(step)
        if step.rawValue < LoadingStep.allCases.count - 1 {
            currentStep = LoadingStep(rawValue: step.rawValue + 1) ?? step
        }
        progress = Double(completedSteps.count) / Double(LoadingStep.allCases.count)
    }
    
    func reset() {
        currentStep = .analyzing
        completedSteps.removeAll()
        progress = 0.0
    }
}

struct ExploreActivitiesView: View {
    @ObservedObject private var exploreManager = ExploreManager.shared
    @ObservedObject private var locationManager = ExploreLocationManager.shared
    @StateObject private var loadingStepManager = LoadingStepManager()
    @EnvironmentObject var themeManager: ThemeManager
    
    // 用户输入状态
    @State private var selectedStartLocation: String = ""
    @State private var isLoadingCurrentLocation = false
    @State private var routeRequirements: String = ""
    @State private var minDistance: Double = 10.0
    @State private var maxDistance: Double = 25.0
    @State private var showLocationPicker = false
    @State private var hasUserInput = false
    @State private var showingRouteDetail = false
    @State private var selectedRoute: ExploreActivity?
    
    // Timer管理状态
    @State private var monitorTimer: Timer?
    @State private var hasProcessedResult = false
    @State private var isGeneratingRoutes = false
    @State private var showRouteGeneration = false
    
    // MARK: - 静态防重复调用变量
    private static var lastCallTime: Date = Date.distantPast
    private static var isAnyInstanceGenerating: Bool = false
    
    var body: some View {
        VStack(spacing: 0) {
            // 头部信息区域
            headerSection
            
            // 用户输入区域
            if !hasUserInput {
                userInputSection
            } else {
                // 内容区域
                if exploreManager.isLoading {
                    loadingView
                } else if exploreManager.exploreActivities.isEmpty {
                    emptyStateView
                } else {
                    routesListView
                }
            }
        }
        .contentShape(Rectangle())
        .onTapGesture {
            hideKeyboard()
        }
        .background(
            Group {
                if themeManager.isDarkMode {
                    Color.black.ignoresSafeArea()
                } else {
                    Color(.systemGroupedBackground).ignoresSafeArea()
                }
            }
        )
        .sheet(item: $selectedRoute) { route in
            ExploreRouteDetailView(route: route, themeManager: themeManager)
        }
        .sheet(isPresented: $showLocationPicker) {
            LocationPickerView(
                selectedLocation: .constant(nil),
                locationName: $selectedStartLocation,
                themeManager: themeManager
            )
        }
        .sheet(isPresented: $showRouteGeneration) {
            RouteGenerationView(
                exploreManager: exploreManager,
                themeManager: themeManager,
                startLocation: selectedStartLocation,
                requirements: routeRequirements.isEmpty ? "生成适合骑行的优质路线，包含风景优美的路段和有趣的景点" : routeRequirements,
                minDistance: minDistance,
                maxDistance: maxDistance
            )
            .presentationDetents([.fraction(0.9)])
            .presentationDragIndicator(.hidden)
            .interactiveDismissDisabled(false)
        }
        .navigationBarHidden(true)
        .onAppear {
            loadDefaultStartLocation()
        }
        .onDisappear {
            // 清理状态，防止状态泄露
            if isGeneratingRoutes || Self.isAnyInstanceGenerating {
                print("🧹 视图消失，清理生成状态")
                resetGenerationState()
            }
            stopMonitorTimer()
        }
    }
    
    // MARK: - 头部区域
    private var headerSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("探索路线")
                    .font(.system(size: 28, weight: .bold))
                    .foregroundColor(themeManager.isDarkMode ? .white : .black)
                
                Spacer()
                
                if hasUserInput {
                    Button(action: {
                        withAnimation(.easeInOut(duration: 0.3)) {
                            hasUserInput = false
                            selectedStartLocation = ""
                            routeRequirements = ""
                            minDistance = 10.0
                            maxDistance = 25.0
                            exploreManager.clearRoutes()
                        }
                    }) {
                        Image(systemName: "gearshape.fill")
                            .font(.system(size: 20, weight: .medium))
                            .foregroundColor(.pulseAccent(isDarkMode: themeManager.isDarkMode))
                            .frame(width: 44, height: 44)
                            .background(
                                Circle()
                                    .fill(.ultraThinMaterial)
                                    .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
                            )
                    }
                    .buttonStyle(.plain)
                }
            }
            
            if !locationManager.currentCity.isEmpty {
                HStack(spacing: 8) {
                    Image(systemName: "location.fill")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.pulseAccent(isDarkMode: themeManager.isDarkMode))
                    
                    Text("当前位置: \(locationManager.currentCity)")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(themeManager.isDarkMode ? .white.opacity(0.8) : .secondary)
                    
                    Spacer()
                }
            }
        }
        .padding(.horizontal, 20)
        .padding(.top, 20)
    }
    
    // MARK: - 用户输入区域
    private var userInputSection: some View {
        VStack(spacing: 24) {
            Spacer()
            
            VStack(spacing: 12) {
                Image(systemName: "sparkles")
                    .font(.system(size: 48))
                    .foregroundColor(.pulseAccent(isDarkMode: themeManager.isDarkMode))
                
                Text("AI定制探索路线")
                    .font(.system(size: 24, weight: .bold))
                    .foregroundColor(themeManager.isDarkMode ? .white : .black)
                
                Text("告诉我您的需求，让AI为您生成专属骑行路线")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(themeManager.isDarkMode ? .white.opacity(0.7) : .secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 20)
            }
            
            Spacer()
            
            VStack(spacing: 20) {
                // 起始位置选择
                VStack(alignment: .leading, spacing: 8) {
                    Text("起始位置")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(themeManager.isDarkMode ? .white : .black)
                    
                    Button(action: {
                        showLocationPicker = true
                    }) {
                        HStack {
                            if isLoadingCurrentLocation {
                                ProgressView()
                                    .scaleEffect(0.8)
                                    .foregroundColor(.pulseAccent(isDarkMode: themeManager.isDarkMode))
                            } else {
                                Image(systemName: "location.circle.fill")
                                    .foregroundColor(.pulseAccent(isDarkMode: themeManager.isDarkMode))
                            }
                            
                            Text(isLoadingCurrentLocation ? "正在获取当前位置..." : 
                                (selectedStartLocation.isEmpty ? "选择起始位置" : selectedStartLocation))
                                .foregroundColor(selectedStartLocation.isEmpty && !isLoadingCurrentLocation ? 
                                    (themeManager.isDarkMode ? .white.opacity(0.5) : .secondary) : 
                                    (themeManager.isDarkMode ? .white : .black))
                                .font(.system(size: 16, weight: .medium))
                            
                            Spacer()
                            
                            if !isLoadingCurrentLocation {
                                Image(systemName: "chevron.down")
                                    .foregroundColor(themeManager.isDarkMode ? .white.opacity(0.5) : .secondary)
                            }
                        }
                        .padding(.horizontal, 16)
                        .padding(.vertical, 14)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(themeManager.isDarkMode ? Color.white.opacity(0.1) : Color.gray.opacity(0.1))
                                .overlay(
                                    RoundedRectangle(cornerRadius: 12)
                                        .stroke(selectedStartLocation.isEmpty ? 
                                            Color.clear : Color.pulseAccent(isDarkMode: themeManager.isDarkMode), lineWidth: 2)
                                )
                        )
                    }
                    .buttonStyle(.plain)
                }
                
                // 路线需求描述
                VStack(alignment: .leading, spacing: 8) {
                    Text("路线需求")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(themeManager.isDarkMode ? .white : .black)
                    
                    ZStack(alignment: .topLeading) {
                        RoundedRectangle(cornerRadius: 12)
                            .fill(themeManager.isDarkMode ? Color.white.opacity(0.05) : Color.gray.opacity(0.05))
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(routeRequirements.isEmpty ? 
                                        (themeManager.isDarkMode ? Color.white.opacity(0.2) : Color.gray.opacity(0.3)) : 
                                        Color.pulseAccent(isDarkMode: themeManager.isDarkMode), lineWidth: 1.5)
                            )
                        
                        TextEditor(text: $routeRequirements)
                            .font(.system(size: 16))
                            .foregroundColor(themeManager.isDarkMode ? .white : .black)
                            .background(Color.clear)
                            .scrollContentBackground(.hidden)
                            .padding(.horizontal, 16)
                            .padding(.vertical, 12)
                        
                        if routeRequirements.isEmpty {
                            Text("例如：想骑15公里，途经公园和咖啡店，难度简单")
                                .foregroundColor(themeManager.isDarkMode ? .white.opacity(0.5) : .secondary)
                                .font(.system(size: 16))
                                .padding(.horizontal, 20)
                                .padding(.vertical, 20)
                                .allowsHitTesting(false)
                        }
                    }
                    .frame(height: 100)
                }
                
                // 距离范围选择
                VStack(alignment: .leading, spacing: 12) {
                    Text("行程距离")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(themeManager.isDarkMode ? .white : .black)
                    
                    VStack(spacing: 16) {
                        HStack {
                            Text("最短距离")
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(themeManager.isDarkMode ? .white.opacity(0.7) : .secondary)
                            
                            Spacer()
                            
                            Text("\(Int(minDistance))公里")
                                .font(.system(size: 16, weight: .semibold))
                                .foregroundColor(.pulseAccent(isDarkMode: themeManager.isDarkMode))
                        }
                        
                        Slider(value: $minDistance, in: 5...200, step: 1)
                            .accentColor(.pulseAccent(isDarkMode: themeManager.isDarkMode))
                            .onChange(of: minDistance) { _, newValue in
                                if newValue > maxDistance {
                                    maxDistance = newValue
                                }
                            }
                        
                        HStack {
                            Text("最远距离")
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(themeManager.isDarkMode ? .white.opacity(0.7) : .secondary)
                            
                            Spacer()
                            
                            Text("\(Int(maxDistance))公里")
                                .font(.system(size: 16, weight: .semibold))
                                .foregroundColor(.pulseAccent(isDarkMode: themeManager.isDarkMode))
                        }
                        
                        Slider(value: $maxDistance, in: 5...200, step: 1)
                            .accentColor(.pulseAccent(isDarkMode: themeManager.isDarkMode))
                            .onChange(of: maxDistance) { _, newValue in
                                if newValue < minDistance {
                                    minDistance = newValue
                                }
                            }
                        
                        HStack {
                            Spacer()
                            Text("路线距离: \(Int(minDistance))-\(Int(maxDistance))公里")
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(.pulseAccent(isDarkMode: themeManager.isDarkMode))
                            Spacer()
                        }
                        .padding(.vertical, 8)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(Color.pulseAccent(isDarkMode: themeManager.isDarkMode).opacity(0.1))
                        )
                    }
                    .padding(16)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(themeManager.isDarkMode ? Color.white.opacity(0.05) : Color.gray.opacity(0.05))
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(Color.pulseAccent(isDarkMode: themeManager.isDarkMode).opacity(0.3), lineWidth: 1)
                            )
                    )
                }
            }
            .padding(.horizontal, 20)
            
            Spacer()
            
            // 生成按钮和重置按钮
            HStack(spacing: 12) {
                // 主要生成按钮
                Button(action: {
                    showRouteGeneration = true
                }) {
                    HStack(spacing: 12) {
                        if isGeneratingRoutes {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: .pulseAccentButtonContent(isDarkMode: themeManager.isDarkMode)))
                                .scaleEffect(0.9)
                        } else {
                            Image(systemName: "sparkles")
                                .font(.system(size: 18, weight: .semibold))
                        }
                        
                        Text(isGeneratingRoutes ? "正在生成路线..." : "智能生成路线")
                            .font(.system(size: 18, weight: .semibold))
                    }
                    .foregroundColor(.pulseAccentButtonContent(isDarkMode: themeManager.isDarkMode))
                    .frame(maxWidth: .infinity)
                    .frame(height: 54)
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(isGeneratingRoutes ? 
                                Color.pulseAccentSoft(isDarkMode: themeManager.isDarkMode).opacity(0.6) :
                                Color.pulseAccentSoft(isDarkMode: themeManager.isDarkMode))
                            .shadow(color: Color.pulseAccent(isDarkMode: themeManager.isDarkMode).opacity(isGeneratingRoutes ? 0.1 : 0.3), 
                                   radius: 12, x: 0, y: 6)
                    )
                }
                .buttonStyle(.plain)
                .disabled(isGeneratingRoutes)
                .opacity(isGeneratingRoutes ? 0.7 : 1.0)
                
                // 重置状态按钮 (只在可能卡住时显示)
                if isGeneratingRoutes || Self.isAnyInstanceGenerating {
                    Button(action: {
                        resetGenerationState()
                    }) {
                        Image(systemName: "arrow.clockwise")
                            .font(.system(size: 16, weight: .semibold))
                            .foregroundColor(.pulseAccent(isDarkMode: themeManager.isDarkMode))
                            .frame(width: 54, height: 54)
                            .background(
                                RoundedRectangle(cornerRadius: 16)
                                    .stroke(Color.pulseAccent(isDarkMode: themeManager.isDarkMode), lineWidth: 2)
                            )
                    }
                    .buttonStyle(.plain)
                }
            }
            .padding(.horizontal, 20)
            .padding(.bottom, 40)
        }
    }
    
    // MARK: - 加载视图
    private var loadingView: some View {
        ZStack {
            // 全屏背景
            LinearGradient(
                gradient: Gradient(colors: [
                    themeManager.isDarkMode ? Color.black : Color(.systemGroupedBackground),
                    Color.pulseAccent(isDarkMode: themeManager.isDarkMode).opacity(0.05)
                ]),
                startPoint: .top,
                endPoint: .bottom
            )
            .ignoresSafeArea()

            VStack(spacing: 0) {
                Spacer()

                VStack(spacing: 60) {
                    // 标题区域
                    VStack(spacing: 20) {
                        Text("🚴‍♂️ 正在生成您的专属路线")
                            .font(.system(size: 32, weight: .bold))
                            .foregroundColor(themeManager.isDarkMode ? .white : .black)
                            .multilineTextAlignment(.center)

                        Text("AI正在为您分析位置信息，规划最佳骑行体验")
                            .font(.system(size: 18, weight: .medium))
                            .foregroundColor(themeManager.isDarkMode ? .white.opacity(0.8) : .secondary)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal, 40)
                    }

                    // 进度指示器
                    VStack(spacing: 40) {
                        ZStack {
                            // 背景圆环
                            Circle()
                                .stroke(
                                    themeManager.isDarkMode ? Color.white.opacity(0.1) : Color.black.opacity(0.08),
                                    lineWidth: 12
                                )
                                .frame(width: 180, height: 180)

                            // 进度圆环
                            Circle()
                                .trim(from: 0, to: loadingStepManager.progress)
                                .stroke(
                                    Color.pulseAccent(isDarkMode: themeManager.isDarkMode),
                                    style: StrokeStyle(lineWidth: 12, lineCap: .round)
                                )
                                .frame(width: 180, height: 180)
                                .rotationEffect(.degrees(-90))
                                .animation(.easeOut(duration: 1.0), value: loadingStepManager.progress)

                            // 中心内容
                            VStack(spacing: 16) {
                                Image(systemName: loadingStepManager.currentStep.icon)
                                    .font(.system(size: 40, weight: .medium))
                                    .foregroundColor(.pulseAccent(isDarkMode: themeManager.isDarkMode))
                                    .animation(.easeInOut(duration: 0.5), value: loadingStepManager.currentStep.icon)

                                Text("\(Int(loadingStepManager.progress * 100))%")
                                    .font(.system(size: 24, weight: .bold))
                                    .foregroundColor(themeManager.isDarkMode ? .white : .black)
                                    .animation(.easeOut(duration: 0.3), value: loadingStepManager.progress)
                            }
                        }

                        // 步骤描述
                        VStack(spacing: 16) {
                            Text(loadingStepManager.currentStep.title)
                                .font(.system(size: 22, weight: .bold))
                                .foregroundColor(themeManager.isDarkMode ? .white : .black)
                                .animation(.easeInOut(duration: 0.5), value: loadingStepManager.currentStep.title)

                            Text(getStepDescription(for: loadingStepManager.currentStep))
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(themeManager.isDarkMode ? .white.opacity(0.8) : .secondary)
                                .multilineTextAlignment(.center)
                                .padding(.horizontal, 40)
                                .animation(.easeInOut(duration: 0.5), value: loadingStepManager.currentStep)
                        }
                    }

                    // 提示信息
                    VStack(spacing: 12) {
                        HStack(spacing: 8) {
                            Image(systemName: "clock.fill")
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(.pulseAccent(isDarkMode: themeManager.isDarkMode))

                            Text("预计需要 30-60 秒")
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(themeManager.isDarkMode ? .white.opacity(0.7) : .secondary)
                        }

                        Text("请保持网络连接，正在为您创造完美的骑行体验")
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(themeManager.isDarkMode ? .white.opacity(0.6) : .secondary)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal, 60)
                    }
                }

                Spacer()
            }
        }
    }
    
    // MARK: - 空状态视图
    private var emptyStateView: some View {
        VStack(spacing: 24) {
            Spacer()
            
            Image(systemName: "map")
                .font(.system(size: 64))
                .foregroundColor(.gray)
            
            VStack(spacing: 8) {
                Text("暂无路线")
                    .font(.system(size: 20, weight: .bold))
                    .foregroundColor(themeManager.isDarkMode ? .white : .black)
                
                Text("请重新生成路线")
                    .font(.system(size: 16))
                    .foregroundColor(themeManager.isDarkMode ? .white.opacity(0.7) : .secondary)
            }
            
            Button("重新设置") {
                withAnimation(.easeInOut(duration: 0.3)) {
                    hasUserInput = false
                    selectedStartLocation = ""
                    routeRequirements = ""
                    minDistance = 10.0
                    maxDistance = 25.0
                    exploreManager.clearRoutes()
                }
            }
            .font(.system(size: 16, weight: .semibold))
            .foregroundColor(.pulseAccent(isDarkMode: themeManager.isDarkMode))
            .padding(.horizontal, 24)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(Color.pulseAccent(isDarkMode: themeManager.isDarkMode), lineWidth: 2)
            )
            
            Spacer()
        }
        .padding(.horizontal, 20)
    }
    
    // MARK: - 路线列表视图
    private var routesListView: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                VStack(spacing: 12) {
                    HStack {
                        VStack(alignment: .leading, spacing: 4) {
                            Text("🎯 基于您的需求定制")
                                .font(.system(size: 14, weight: .semibold))
                                .foregroundColor(.pulseAccent(isDarkMode: themeManager.isDarkMode))
                            
                            Text("起始位置: \(selectedStartLocation)")
                                .font(.system(size: 12))
                                .foregroundColor(themeManager.isDarkMode ? .white.opacity(0.6) : .secondary)
                            
                            if !routeRequirements.isEmpty {
                                Text("需求: \(routeRequirements)")
                                    .font(.system(size: 12))
                                    .foregroundColor(themeManager.isDarkMode ? .white.opacity(0.6) : .secondary)
                                    .lineLimit(1)
                            }
                        }
                        
                        Spacer()
                        
                        Button("重新生成") {
                            exploreManager.clearRoutes()
                            showRouteGeneration = true
                        }
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.pulseAccent(isDarkMode: themeManager.isDarkMode))
                        .padding(.horizontal, 12)
                        .padding(.vertical, 8)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(Color.pulseAccent(isDarkMode: themeManager.isDarkMode).opacity(0.3), lineWidth: 1)
                        )
                    }
                }
                .padding(.horizontal, 20)
                .padding(.top, 16)
                
                ForEach(exploreManager.exploreActivities.indices, id: \.self) { index in
                    let route = exploreManager.exploreActivities[index]
                    ExploreRouteCard(
                        route: route, 
                        themeManager: themeManager,
                        onTap: {
                            selectedRoute = route
                            showingRouteDetail = true
                        },
                        onGenerateSnapshot: {
                            generateMapSnapshot(for: route, at: index)
                        }
                    )
                    .padding(.horizontal, 20)
                }
            }
            .padding(.bottom, 20)
        }
    }
    
    // MARK: - 辅助方法
    private func hideKeyboard() {
        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
    }
    
    private func loadDefaultStartLocation() {
        guard selectedStartLocation.isEmpty else { return }
        guard !isLoadingCurrentLocation else { return }
        
        isLoadingCurrentLocation = true
        
        Task {
            if let currentLocation = await WeatherLocationManager.shared.checkPermissionAndGetLocation() {
                let geocoder = CLGeocoder()
                do {
                    let placemarks = try await geocoder.reverseGeocodeLocation(currentLocation)
                    
                    await MainActor.run {
                        if let placemark = placemarks.first {
                            var locationName = ""
                            
                            if let locality = placemark.locality {
                                locationName = locality
                            } else if let administrativeArea = placemark.administrativeArea {
                                locationName = administrativeArea
                            } else if let country = placemark.country {
                                locationName = country
                            }
                            
                            if !locationName.isEmpty {
                                selectedStartLocation = locationName
                            } else {
                                selectedStartLocation = locationManager.currentCity.isEmpty ? "当前位置" : locationManager.currentCity
                            }
                        } else {
                            selectedStartLocation = locationManager.currentCity.isEmpty ? "当前位置" : locationManager.currentCity
                        }
                        
                        isLoadingCurrentLocation = false
                    }
                } catch {
                    await MainActor.run {
                        selectedStartLocation = locationManager.currentCity.isEmpty ? "当前位置" : locationManager.currentCity
                        isLoadingCurrentLocation = false
                    }
                }
            } else {
                await MainActor.run {
                    selectedStartLocation = locationManager.currentCity.isEmpty ? "当前位置" : locationManager.currentCity
                    isLoadingCurrentLocation = false
                }
            }
        }
    }
    
    private func generateCustomRoutes() {
        // 添加严格的重复调用保护 - 全局级别
        let currentTime = Date()
        
        // 添加调试信息
        print("🐛 DEBUG - generateCustomRoutes调用:")
        print("  - isAnyInstanceGenerating: \(Self.isAnyInstanceGenerating)")
        print("  - isGeneratingRoutes: \(isGeneratingRoutes)")
        print("  - exploreManager.isLoading: \(exploreManager.isLoading)")
        print("  - timeSinceLastCall: \(currentTime.timeIntervalSince(Self.lastCallTime))")
        
        // 检查是否有任何实例正在生成
        guard !Self.isAnyInstanceGenerating else {
            print("⚠️ generateCustomRoutes被调用，但全局已有实例正在生成路线，忽略重复调用")
            return
        }
        
        // 如果当前实例正在生成，立即返回
        guard !isGeneratingRoutes else {
            print("⚠️ generateCustomRoutes被调用但当前实例正在生成路线，忽略重复调用")
            return
        }
        
        // 检查最近是否有调用（防止快速连击）- 2秒冷却时间（减少到2秒）
        let timeSinceLastCall = currentTime.timeIntervalSince(Self.lastCallTime)
        if timeSinceLastCall < 2.0 {
            print("⚠️ generateCustomRoutes被调用距离上次调用仅\(String(format: "%.1f", timeSinceLastCall))秒，忽略快速重复调用")
            return
        }
        
        // 放宽ExploreManager加载检查 - 只在确实正在进行请求时阻止
        if exploreManager.isLoading && timeSinceLastCall < 10.0 {
            print("⚠️ generateCustomRoutes被调用但ExploreManager正在加载且距离上次调用不足10秒，忽略重复调用")
            return
        }
        
        // 如果ExploreManager长时间处于加载状态，强制重置
        if exploreManager.isLoading && timeSinceLastCall >= 10.0 {
            print("🔄 检测到ExploreManager长时间处于加载状态，强制重置状态")
            resetGenerationState()
        }
        
        // 设置全局锁定状态
        Self.lastCallTime = currentTime
        Self.isAnyInstanceGenerating = true
        isGeneratingRoutes = true
        
        print("🎯 generateCustomRoutes开始执行，时间戳: \(currentTime.timeIntervalSince1970)")
        print("🔒 设置全局生成锁定状态")
        
        stopMonitorTimer()
        hideKeyboard()
        
        withAnimation(.easeInOut(duration: 0.3)) {
            hasUserInput = true
        }
        
        loadingStepManager.reset()
        loadingStepManager.setStep(.analyzing)
        
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
        
        let startLocation = selectedStartLocation.isEmpty ? 
            (locationManager.currentCity.isEmpty ? "成都" : locationManager.currentCity) : selectedStartLocation
        
        let requirements = routeRequirements.isEmpty ? 
            "生成适合骑行的优质路线，包含风景优美的路段和有趣的景点" : routeRequirements
        
        print("🎯 生成路线 - 起始位置: \(startLocation), 需求: \(requirements), 距离: \(Int(minDistance))-\(Int(maxDistance))公里")
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            self.loadingStepManager.completeStep(.analyzing)
            self.loadingStepManager.setStep(.planning)
            
            self.exploreManager.generateCustomRoutes(
                startLocation: startLocation,
                requirements: requirements,
                minDistance: self.minDistance,
                maxDistance: self.maxDistance
            )
            
            self.monitorRouteGeneration()
        }
    }
    
    // 添加状态重置方法
    private func resetGenerationState() {
        print("🔄 重置生成状态")
        Self.isAnyInstanceGenerating = false
        isGeneratingRoutes = false
        hasProcessedResult = false
        stopMonitorTimer()
        
        // 重置加载步骤管理器
        loadingStepManager.reset()
        
        print("✅ 状态重置完成")
    }
    
    private func monitorRouteGeneration() {
        stopMonitorTimer()
        hasProcessedResult = false
        
        let startTime = Date()
        let timeoutInterval: TimeInterval = 180.0
        
        monitorTimer = Timer.scheduledTimer(withTimeInterval: 0.5, repeats: true) { timer in
            
            DispatchQueue.main.async {
                // 超时处理
                if Date().timeIntervalSince(startTime) > timeoutInterval {
                    print("⚠️ 路线生成超时，释放全局锁定状态")
                    hasProcessedResult = true
                    stopMonitorTimer()
                    loadingStepManager.completeStep(.planning)
                    isGeneratingRoutes = false
                    Self.isAnyInstanceGenerating = false  // 释放全局锁定
                    return
                }
                
                if hasProcessedResult { return }
                
                if !exploreManager.isLoading {
                    if !exploreManager.exploreActivities.isEmpty {
                        print("✅ 路线生成成功，共\(exploreManager.exploreActivities.count)条路线")
                        print("🔓 释放全局生成锁定状态")
                        
                        hasProcessedResult = true
                        stopMonitorTimer()
                        
                        loadingStepManager.completeStep(.planning)
                        loadingStepManager.setStep(.generating)
                        
                        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                            loadingStepManager.completeStep(.generating)
                            loadingStepManager.setStep(.snapshot)
                            
                            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                                loadingStepManager.completeStep(.snapshot)
                                isGeneratingRoutes = false
                                Self.isAnyInstanceGenerating = false  // 释放全局锁定
                            }
                        }
                    } else if exploreManager.error != nil {
                        print("❌ 路线生成失败")
                        print("🔓 释放全局生成锁定状态（错误）")
                        hasProcessedResult = true
                        stopMonitorTimer()
                        loadingStepManager.completeStep(.planning)
                        isGeneratingRoutes = false
                        Self.isAnyInstanceGenerating = false  // 释放全局锁定
                    }
                }
            }
        }
    }
    
    private func stopMonitorTimer() {
        monitorTimer?.invalidate()
        monitorTimer = nil
    }
    
    private func getStepDescription(for step: LoadingStepManager.LoadingStep) -> String {
        switch step {
        case .analyzing: return "正在分析您的位置和骑行需求"
        case .planning: return "AI正在为您规划最佳路线方案"
        case .generating: return "生成详细的轨迹路径信息"
        case .snapshot: return "制作精美的路线预览图"
        }
    }
    
    // MARK: - 地图快照生成
    private func generateMapSnapshot(for route: ExploreActivity, at index: Int) {
        guard route.mapSnapshot == nil,
              !route.gpxTrack.isEmpty else { return }
        
        Task {
            do {
                print("🗺️ 开始为路线 \(route.title) 生成地图快照")
                let snapshot = try await createMapboxSnapshot(for: route, themeManager: themeManager)
                
                await MainActor.run {
                    if index < exploreManager.exploreActivities.count {
                        exploreManager.exploreActivities[index].mapSnapshot = snapshot
                        print("✅ 地图快照生成成功: \(route.title)")
                    }
                }
            } catch {
                print("❌ 地图快照生成失败: \(error.localizedDescription)")
            }
        }
    }
    
    // MARK: - MapKit Snapshotter API
    private func createMapboxSnapshot(for route: ExploreActivity, themeManager: ThemeManager) async throws -> UIImage {
        guard !route.gpxTrack.isEmpty else {
            throw MapboxError.insufficientTrackPoints
        }
        
        print("🗺️ 使用MapKit生成地图快照")
        print("  - 轨迹点数量: \(route.gpxTrack.count)")
        print("  - 途径点数量: \(route.waypoints.count)")
        
        return try await withCheckedThrowingContinuation { continuation in
            // 获取所有需要显示的坐标点
            var allCoordinates = route.gpxTrack.map { trackPoint in
                CLLocationCoordinate2D(latitude: trackPoint.latitude, longitude: trackPoint.longitude)
            }
            
            // 添加途径点坐标
            for waypoint in route.waypoints {
                let waypointCoord = findNearestTrackPoint(for: waypoint, in: route.gpxTrack)
                let coordinate = CLLocationCoordinate2D(latitude: waypointCoord.latitude, longitude: waypointCoord.longitude)
                allCoordinates.append(coordinate)
            }
            
            print("🗺️ 计算地图区域，总坐标点数: \(allCoordinates.count)")
            
            // 基于所有坐标点计算地图区域
            let mapRegion = calculateMapRegion(for: allCoordinates)
            
            // 创建快照选项
            let options = MKMapSnapshotter.Options()
            options.region = mapRegion
            options.size = CGSize(width: 500, height: 400)
            options.scale = UIScreen.main.scale
            // 根据主题选择合适的地图样式，避免使用卫星地图
            options.mapType = themeManager.isDarkMode ? .mutedStandard : .standard
            
            // 创建快照生成器
            let snapshotter = MKMapSnapshotter(options: options)
            
            // 生成快照
            snapshotter.start { snapshot, error in
                if let error = error {
                    print("❌ MapKit快照生成失败: \(error)")
                    continuation.resume(throwing: MapboxError.serverError(0, error.localizedDescription))
                    return
                }
                
                guard let snapshot = snapshot else {
                    print("❌ 快照为空")
                    continuation.resume(throwing: MapboxError.invalidImageData)
                    return
                }
                
                print("✅ MapKit快照生成成功")
                // 在快照上绘制路线和标记
                let enhancedImage = self.drawRouteOnMapSnapshot(snapshot, route: route, themeManager: themeManager)
                continuation.resume(returning: enhancedImage)
            }
        }
    }
    
    // MARK: - MapKit 辅助方法
    private func calculateMapRegion(for coordinates: [CLLocationCoordinate2D]) -> MKCoordinateRegion {
        guard !coordinates.isEmpty else {
            return MKCoordinateRegion()
        }
        
        let latitudes = coordinates.map { $0.latitude }
        let longitudes = coordinates.map { $0.longitude }
        
        let minLat = latitudes.min()!
        let maxLat = latitudes.max()!
        let minLon = longitudes.min()!
        let maxLon = longitudes.max()!
        
        let center = CLLocationCoordinate2D(
            latitude: (minLat + maxLat) / 2,
            longitude: (minLon + maxLon) / 2
        )
        
        // 增加更多边距确保标记点完全显示（40%边距）
        let latitudeDelta = max((maxLat - minLat) * 1.4, 0.01)  // 最小增量防止过小
        let longitudeDelta = max((maxLon - minLon) * 1.4, 0.01)
        
        let span = MKCoordinateSpan(
            latitudeDelta: latitudeDelta,
            longitudeDelta: longitudeDelta
        )
        
        print("🗺️ 地图区域 - 中心: \(center), 范围: \(span)")
        
        return MKCoordinateRegion(center: center, span: span)
    }
    
    private func drawRouteOnMapSnapshot(_ snapshot: MKMapSnapshotter.Snapshot, route: ExploreActivity, themeManager: ThemeManager) -> UIImage {
        let image = snapshot.image
        
        UIGraphicsBeginImageContextWithOptions(image.size, false, image.scale)
        
        // 绘制基础地图
        image.draw(at: .zero)
        
        // 获取绘图上下文
        guard let context = UIGraphicsGetCurrentContext() else {
            UIGraphicsEndImageContext()
            return image
        }
        
        // 绘制路线
        let coordinates = route.gpxTrack.map { CLLocationCoordinate2D(latitude: $0.latitude, longitude: $0.longitude) }
        drawRouteOnContext(context: context, coordinates: coordinates, snapshot: snapshot)
        
        // 绘制标记点
        drawMarkersOnContext(context: context, route: route, snapshot: snapshot)
        
        let resultImage = UIGraphicsGetImageFromCurrentImageContext() ?? image
        UIGraphicsEndImageContext()
        
        return resultImage
    }
    
    private func drawRouteOnContext(context: CGContext, coordinates: [CLLocationCoordinate2D], snapshot: MKMapSnapshotter.Snapshot) {
        guard coordinates.count > 1 else { return }
        
        // 设置路线样式（银光绿色 #00FF94）
        context.setStrokeColor(UIColor(red: 0, green: 1, blue: 0.58, alpha: 1).cgColor)
        context.setLineWidth(4.0)
        context.setLineCap(.round)
        context.setLineJoin(.round)
        
        // 绘制路线路径
        let path = UIBezierPath()
        var validPoints: [CGPoint] = []
        
        for coordinate in coordinates {
            let point = snapshot.point(for: coordinate)
            
            // 检查点是否有效（不是NaN或无穷大）
            guard point.x.isFinite && point.y.isFinite else {
                print("⚠️ 跳过无效坐标点: \(coordinate) -> \(point)")
                continue
            }
            
            validPoints.append(point)
        }
        
        // 确保有足够的有效点来绘制路径
        guard validPoints.count > 1 else {
            print("⚠️ 有效坐标点不足，无法绘制路径")
            return
        }
        
        // 使用有效点绘制路径
        for (index, point) in validPoints.enumerated() {
            if index == 0 {
                path.move(to: point)
            } else {
                path.addLine(to: point)
            }
        }
        
        context.addPath(path.cgPath)
        context.strokePath()
    }
    
    private func drawMarkersOnContext(context: CGContext, route: ExploreActivity, snapshot: MKMapSnapshotter.Snapshot) {
        guard !route.gpxTrack.isEmpty else { return }
        
        // 起点标记（鲜绿色）
        let startPoint = route.gpxTrack.first!
        let startCoord = CLLocationCoordinate2D(latitude: startPoint.latitude, longitude: startPoint.longitude)
        drawEnhancedMarkerOnContext(context: context, coordinate: startCoord, text: "起", type: .start, snapshot: snapshot)
        
        // 终点标记（鲜红色）
        let endPoint = route.gpxTrack.last!
        let endCoord = CLLocationCoordinate2D(latitude: endPoint.latitude, longitude: endPoint.longitude)
        drawEnhancedMarkerOnContext(context: context, coordinate: endCoord, text: "终", type: .end, snapshot: snapshot)
        
        // 途径点标记（鲜蓝色）
        for (index, waypoint) in route.waypoints.enumerated() {
            let coord = findNearestTrackPoint(for: waypoint, in: route.gpxTrack)
            let coordinate = CLLocationCoordinate2D(latitude: coord.latitude, longitude: coord.longitude)
            drawEnhancedMarkerOnContext(context: context, coordinate: coordinate, text: "\(index + 1)", type: .waypoint, snapshot: snapshot)
        }
    }
    
    // 标记类型枚举
    private enum MarkerType {
        case start
        case end
        case waypoint
        
        var colors: (primary: UIColor, secondary: UIColor, shadow: UIColor) {
            switch self {
            case .start:
                // 绿色系 - 起点
                return (UIColor(red: 0.2, green: 0.8, blue: 0.2, alpha: 1.0), 
                       UIColor(red: 0.1, green: 0.6, blue: 0.1, alpha: 1.0),
                       UIColor(red: 0.1, green: 0.4, blue: 0.1, alpha: 0.4))
            case .end:
                // 红色系 - 终点
                return (UIColor(red: 0.9, green: 0.2, blue: 0.2, alpha: 1.0), 
                       UIColor(red: 0.7, green: 0.1, blue: 0.1, alpha: 1.0),
                       UIColor(red: 0.5, green: 0.1, blue: 0.1, alpha: 0.4))
            case .waypoint:
                // 蓝色系 - 途径点
                return (UIColor(red: 0.2, green: 0.5, blue: 0.9, alpha: 1.0), 
                       UIColor(red: 0.1, green: 0.3, blue: 0.7, alpha: 1.0),
                       UIColor(red: 0.1, green: 0.2, blue: 0.5, alpha: 0.4))
            }
        }
        
        var iconPath: UIBezierPath? {
            switch self {
            case .start:
                // 播放按钮形状 (▶️)
                let path = UIBezierPath()
                path.move(to: CGPoint(x: -4, y: -6))
                path.addLine(to: CGPoint(x: 6, y: 0))
                path.addLine(to: CGPoint(x: -4, y: 6))
                path.close()
                return path
            case .end:
                // 方形停止按钮 (⏹️)
                return UIBezierPath(rect: CGRect(x: -5, y: -5, width: 10, height: 10))
            case .waypoint:
                // 圆点
                return UIBezierPath(ovalIn: CGRect(x: -3, y: -3, width: 6, height: 6))
            }
        }
    }
    
    private func drawEnhancedMarkerOnContext(context: CGContext, coordinate: CLLocationCoordinate2D, text: String, type: MarkerType, snapshot: MKMapSnapshotter.Snapshot) {
        let point = snapshot.point(for: coordinate)
        
        // 检查点是否有效（不是NaN或无穷大）
        guard point.x.isFinite && point.y.isFinite else {
            print("⚠️ 跳过无效标记坐标: \(coordinate) -> \(point)")
            return
        }
        
        let radius: CGFloat = 20  // 增大标记点
        let colors = type.colors
        
        // 保存当前图形状态
        context.saveGState()
        
        // 1. 绘制投影阴影（多层阴影效果）
        for i in 0..<3 {
            let shadowOffset = CGFloat(3 - i)
            let shadowAlpha = 0.15 - (CGFloat(i) * 0.05)
            let shadowRadius = radius + CGFloat(i)
            
            let shadowRect = CGRect(
                x: point.x - shadowRadius + shadowOffset,
                y: point.y - shadowRadius + shadowOffset,
                width: shadowRadius * 2,
                height: shadowRadius * 2
            )
            
            guard shadowRect.isFinite else { continue }
            
            context.setFillColor(colors.shadow.withAlphaComponent(shadowAlpha).cgColor)
            context.fillEllipse(in: shadowRect)
        }
        
        // 2. 绘制白色外圈（边框效果）
        let borderWidth: CGFloat = 3
        let outerRadius = radius + borderWidth
        let outerRect = CGRect(
            x: point.x - outerRadius,
            y: point.y - outerRadius,
            width: outerRadius * 2,
            height: outerRadius * 2
        )
        
        guard outerRect.isFinite else {
            context.restoreGState()
            return
        }
        
        context.setFillColor(UIColor.white.cgColor)
        context.fillEllipse(in: outerRect)
        
        // 3. 绘制主体圆形（渐变背景）
        let markerRect = CGRect(
            x: point.x - radius,
            y: point.y - radius,
            width: radius * 2,
            height: radius * 2
        )
        
        guard markerRect.isFinite else {
            context.restoreGState()
            return
        }
        
        // 创建径向渐变
        let colorSpace = CGColorSpaceCreateDeviceRGB()
        let gradientColors = [colors.primary.cgColor, colors.secondary.cgColor]
        guard let gradient = CGGradient(colorsSpace: colorSpace, colors: gradientColors as CFArray, locations: [0.0, 1.0]) else {
            context.restoreGState()
            return
        }
        
        // 剪切到圆形区域并绘制渐变
        context.addEllipse(in: markerRect)
        context.clip()
        
        let centerPoint = CGPoint(x: point.x, y: point.y)
        context.drawRadialGradient(gradient, startCenter: centerPoint, startRadius: 0,
                                 endCenter: centerPoint, endRadius: radius, options: [])
        
        // 重置剪切区域
        context.resetClip()
        
        // 4. 绘制内部高光
        let highlightRadius = radius * 0.3
        let highlightCenter = CGPoint(x: point.x - radius * 0.2, y: point.y - radius * 0.3)
        let highlightRect = CGRect(
            x: highlightCenter.x - highlightRadius,
            y: highlightCenter.y - highlightRadius,
            width: highlightRadius * 2,
            height: highlightRadius * 2
        )
        
        if highlightRect.isFinite {
            context.setFillColor(UIColor.white.withAlphaComponent(0.3).cgColor)
            context.fillEllipse(in: highlightRect)
        }
        
        // 5. 绘制图标（基于类型）
        if let iconPath = type.iconPath {
            context.saveGState()
            context.translateBy(x: point.x, y: point.y)
            
            // 绘制图标阴影
            context.saveGState()
            context.translateBy(x: 1, y: 1)
            context.addPath(iconPath.cgPath)
            context.setFillColor(UIColor.black.withAlphaComponent(0.3).cgColor)
            context.fillPath()
            context.restoreGState()
            
            // 绘制图标
            context.addPath(iconPath.cgPath)
            context.setFillColor(UIColor.white.cgColor)
            context.fillPath()
            
            context.restoreGState()
        }
        
        // 6. 绘制文字（仅对途径点显示数字）
        if type == .waypoint {
            let fontSize: CGFloat = 10
            let attributes: [NSAttributedString.Key: Any] = [
                .font: UIFont.systemFont(ofSize: fontSize, weight: .bold),
                .foregroundColor: UIColor.white,
                .strokeColor: UIColor.black.withAlphaComponent(0.5),
                .strokeWidth: -2.0
            ]
            
            let textSize = text.size(withAttributes: attributes)
            
            guard textSize.width.isFinite && textSize.height.isFinite else {
                context.restoreGState()
                return
            }
            
            let textRect = CGRect(
                x: point.x - textSize.width / 2,
                y: point.y + radius + 8,  // 文字显示在圆形下方
                width: textSize.width,
                height: textSize.height
            )
            
            if textRect.isFinite {
                // 绘制文字背景
                let textBgRect = CGRect(
                    x: textRect.minX - 4,
                    y: textRect.minY - 2,
                    width: textRect.width + 8,
                    height: textRect.height + 4
                )
                
                context.setFillColor(UIColor.black.withAlphaComponent(0.7).cgColor)
                let bgPath = UIBezierPath(roundedRect: textBgRect, cornerRadius: 3)
                context.addPath(bgPath.cgPath)
                context.fillPath()
                
                text.draw(in: textRect, withAttributes: attributes)
            }
        }
        
        // 恢复图形状态
        context.restoreGState()
    }
    
    // MARK: - 辅助函数
    private func findNearestTrackPoint(for waypoint: Waypoint, in trackPoints: [ExploreTrackPoint]) -> (longitude: Double, latitude: Double) {
        // 如果轨迹点中有同名点，优先使用
        if let exactMatch = trackPoints.first(where: { $0.name?.contains(waypoint.name) == true }) {
            return (longitude: exactMatch.longitude, latitude: exactMatch.latitude)
        }
        
        // 否则根据类型选择合适的点
        switch waypoint.type {
        case "起点":
            let firstPoint = trackPoints.first ?? trackPoints[0]
            return (longitude: firstPoint.longitude, latitude: firstPoint.latitude)
        case "终点":
            let lastPoint = trackPoints.last ?? trackPoints[trackPoints.count - 1]
            return (longitude: lastPoint.longitude, latitude: lastPoint.latitude)
        default:
            // 为中间点选择轨迹中间位置的点
            let middleIndex = trackPoints.count / 2
            let middlePoint = trackPoints[min(middleIndex, trackPoints.count - 1)]
            return (longitude: middlePoint.longitude, latitude: middlePoint.latitude)
        }
    }
}

// MARK: - 路线卡片视图
struct ExploreRouteCard: View {
    let route: ExploreActivity
    let themeManager: ThemeManager
    let onTap: () -> Void
    let onGenerateSnapshot: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: 16) {
                // 地图快照区域
                ZStack {
                    RoundedRectangle(cornerRadius: 12)
                        .fill(.ultraThinMaterial.opacity(0.3))
                        .frame(height: 160)
                    
                    if let snapshot = route.mapSnapshot {
                        Image(uiImage: snapshot)
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(height: 160)
                            .clipped()
                            .cornerRadius(12)
                    } else {
                        VStack(spacing: 8) {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: .pulseAccent(isDarkMode: themeManager.isDarkMode)))
                                .scaleEffect(1.2)
                            
                            Text("生成路线图")
                                .font(.system(size: 12, weight: .medium))
                                .foregroundColor(.pulseAccent(isDarkMode: themeManager.isDarkMode))
                        }
                        .onAppear {
                            onGenerateSnapshot()
                        }
                    }
                    
                    // 覆盖图标和信息
                    VStack {
                        HStack {
                            // 起始点标记 (绿色)
                            HStack(spacing: 4) {
                                Circle()
                                    .fill(Color.green)
                                    .frame(width: 8, height: 8)
                                Text("起点")
                                    .font(.system(size: 10, weight: .medium))
                                    .foregroundColor(.white)
                            }
                            .padding(.horizontal, 6)
                            .padding(.vertical, 3)
                            .background(
                                Capsule()
                                    .fill(.black.opacity(0.7))
                            )
                            
                            Spacer()
                            
                            // 终点标记 (红色)
                            HStack(spacing: 4) {
                                Text("终点")
                                    .font(.system(size: 10, weight: .medium))
                                    .foregroundColor(.white)
                                Circle()
                                    .fill(Color.red)
                                    .frame(width: 8, height: 8)
                            }
                            .padding(.horizontal, 6)
                            .padding(.vertical, 3)
                            .background(
                                Capsule()
                                    .fill(.black.opacity(0.7))
                            )
                        }
                        
                        Spacer()
                        
                        // 距离信息
                        HStack {
                            Spacer()
                            Text("\(String(format: "%.1f", route.distance)) km")
                                .font(.system(size: 12, weight: .bold))
                                .foregroundColor(.white)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 4)
                                .background(
                                    Capsule()
                                        .fill(Color.pulseAccent(isDarkMode: themeManager.isDarkMode).opacity(0.9))
                                )
                        }
                    }
                    .padding(8)
                }
                
                // 路线信息
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Text(route.title)
                            .font(.system(size: 18, weight: .bold))
                            .foregroundColor(themeManager.isDarkMode ? .white : .black)
                            .multilineTextAlignment(.leading)
                        
                        Spacer()
                        
                        Image(systemName: "arrow.right")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(.pulseAccent(isDarkMode: themeManager.isDarkMode))
                    }
                    
                    Text(route.description)
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(themeManager.isDarkMode ? .white.opacity(0.7) : .gray)
                        .multilineTextAlignment(.leading)
                        .lineLimit(2)
                    
                    HStack(spacing: 16) {
                        HStack(spacing: 6) {
                            Image(systemName: "speedometer")
                                .font(.system(size: 12, weight: .medium))
                                .foregroundColor(.pulseAccent(isDarkMode: themeManager.isDarkMode))
                            
                            Text(route.difficulty)
                                .font(.system(size: 12, weight: .medium))
                                .foregroundColor(themeManager.isDarkMode ? .white.opacity(0.8) : .secondary)
                        }
                        
                        if !route.gpxTrack.isEmpty {
                            HStack(spacing: 6) {
                                Image(systemName: "location.fill")
                                    .font(.system(size: 12, weight: .medium))
                                    .foregroundColor(.pulseAccent(isDarkMode: themeManager.isDarkMode))
                                
                                Text("\(route.gpxTrack.count) 个点位")
                                    .font(.system(size: 12, weight: .medium))
                                    .foregroundColor(themeManager.isDarkMode ? .white.opacity(0.8) : .secondary)
                            }
                        }
                        
                        Spacer()
                    }
                }
            }
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(.ultraThinMaterial.opacity(themeManager.isDarkMode ? 0.2 : 0.8))
                    .shadow(
                        color: themeManager.isDarkMode ? .black.opacity(0.2) : .black.opacity(0.08),
                        radius: 8, x: 0, y: 4
                    )
            )
        }
        .buttonStyle(.plain)
    }
}

// ExploreRouteDetailView 已移动到独立文件 ExploreRouteDetailView.swift

// MARK: - CGRect扩展
extension CGRect {
    /// 检查CGRect的所有值是否都是有限数（不是NaN或无穷大）
    var isFinite: Bool {
        return origin.x.isFinite && origin.y.isFinite && 
               size.width.isFinite && size.height.isFinite &&
               !size.width.isNaN && !size.height.isNaN &&
               !origin.x.isNaN && !origin.y.isNaN
    }
} 