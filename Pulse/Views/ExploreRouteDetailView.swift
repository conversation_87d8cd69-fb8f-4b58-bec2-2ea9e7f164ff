import SwiftUI
import MapKit

// MARK: - 路线详情视图
struct ExploreRouteDetailView: View {
    let route: ExploreActivity
    let themeManager: ThemeManager
    @Environment(\.dismiss) private var dismiss
    @State private var showFullScreenMap = false
    @State private var mapSnapshot: UIImage?
    @State private var isLoadingSnapshot = true

    var body: some View {
        NavigationView {
            ZStack {
                // 全屏背景
                (themeManager.isDarkMode ? Color.black : Color(.systemGroupedBackground))
                    .ignoresSafeArea()

                VStack(spacing: 0) {
                    // 地图快照区域 - 可点击查看大图
                    mapSnapshotSection

                    ScrollView {
                        VStack(alignment: .leading, spacing: 20) {
                            // 路线基本信息
                            routeBasicInfoView

                            // 路线统计信息
                            routeStatsView

                            // 亮点特色
                            if !route.highlights.isEmpty {
                                routeHighlightsView
                            }

                            // 途径点信息
                            if !route.waypoints.isEmpty {
                                waypointsView
                            }

                            // 操作按钮
                            actionButtonsView

                            // 底部安全区域
                            Color.clear.frame(height: 20)
                        }
                        .padding(.horizontal, 20)
                        .padding(.top, 10)
                    }
                }
            }
            .navigationTitle("路线详情")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("关闭") {
                        dismiss()
                    }
                    .foregroundColor(.pulseAccent(isDarkMode: themeManager.isDarkMode))
                }
            }
        }
        .sheet(isPresented: $showFullScreenMap) {
            ExploreRouteMapView(route: route, themeManager: themeManager)
        }
        .onAppear {
            generateMapSnapshot()
        }
    }


    
    // MARK: - 地图快照区域
    private var mapSnapshotSection: some View {
        Button(action: {
            showFullScreenMap = true
        }) {
            ZStack {
                RoundedRectangle(cornerRadius: 16)
                    .fill(.ultraThinMaterial.opacity(0.3))
                    .frame(height: 300)

                if isLoadingSnapshot {
                    VStack(spacing: 16) {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .pulseAccent(isDarkMode: themeManager.isDarkMode)))
                            .scaleEffect(1.8)

                        Text("生成路线预览")
                            .font(.system(size: 18, weight: .semibold))
                            .foregroundColor(.pulseAccent(isDarkMode: themeManager.isDarkMode))

                        Text("正在为您创建精美的路线地图...")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(themeManager.isDarkMode ? .white.opacity(0.7) : .secondary)
                            .multilineTextAlignment(.center)
                    }
                    .padding(40)
                } else if let snapshot = mapSnapshot {
                    Image(uiImage: snapshot)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(height: 300)
                        .clipShape(RoundedRectangle(cornerRadius: 16))
                        .overlay(
                            VStack {
                                Spacer()
                                HStack {
                                    Spacer()
                                    HStack(spacing: 8) {
                                        Image(systemName: "hand.tap.fill")
                                            .font(.system(size: 12, weight: .medium))
                                        Text("点击查看详细地图")
                                            .font(.system(size: 14, weight: .semibold))
                                    }
                                    .foregroundColor(.white)
                                    .padding(.horizontal, 16)
                                    .padding(.vertical, 8)
                                    .background(.ultraThinMaterial)
                                    .clipShape(RoundedRectangle(cornerRadius: 12))
                                    .padding()
                                }
                            }
                        )
                        .shadow(color: .black.opacity(0.2), radius: 8, x: 0, y: 4)
                } else {
                    VStack(spacing: 16) {
                        Image(systemName: "map.fill")
                            .font(.system(size: 40))
                            .foregroundColor(.pulseAccent(isDarkMode: themeManager.isDarkMode))

                        Text("点击查看路线地图")
                            .font(.system(size: 18, weight: .semibold))
                            .foregroundColor(.pulseAccent(isDarkMode: themeManager.isDarkMode))

                        Text("探索这条精心设计的骑行路线")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(themeManager.isDarkMode ? .white.opacity(0.7) : .secondary)
                    }
                    .padding(40)
                }
            }
        }
        .buttonStyle(.plain)
        .padding(.horizontal, 20)
    }
    
    // MARK: - 路线基本信息
    private var routeBasicInfoView: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text(route.title)
                .font(.system(size: 24, weight: .bold))
                .foregroundColor(themeManager.isDarkMode ? .white : .black)
            
            Text(route.description)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(themeManager.isDarkMode ? .white.opacity(0.8) : .secondary)
                .lineLimit(nil)
        }
    }
    
    // MARK: - 路线统计信息
    private var routeStatsView: some View {
        HStack(spacing: 20) {
            RouteStatItem(
                icon: "road.lanes",
                title: "距离",
                value: "\(String(format: "%.1f", route.distance)) km",
                themeManager: themeManager
            )

            RouteStatItem(
                icon: "clock.fill",
                title: "时长",
                value: route.duration,
                themeManager: themeManager
            )

            RouteStatItem(
                icon: "speedometer",
                title: "难度",
                value: route.difficulty,
                themeManager: themeManager
            )

            Spacer()
        }
    }
    
    // MARK: - 亮点特色
    private var routeHighlightsView: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("路线亮点")
                .font(.system(size: 18, weight: .bold))
                .foregroundColor(themeManager.isDarkMode ? .white : .black)
            
            ForEach(route.highlights, id: \.self) { highlight in
                HStack(spacing: 12) {
                    Image(systemName: "star.fill")
                        .font(.system(size: 12))
                        .foregroundColor(.pulseAccent(isDarkMode: themeManager.isDarkMode))
                    
                    Text(highlight)
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(themeManager.isDarkMode ? .white.opacity(0.8) : .secondary)
                    
                    Spacer()
                }
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial.opacity(themeManager.isDarkMode ? 0.2 : 0.8))
        )
    }
    
    // MARK: - 途径点信息
    private var waypointsView: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("途径地点")
                .font(.system(size: 18, weight: .bold))
                .foregroundColor(themeManager.isDarkMode ? .white : .black)
            
            ForEach(Array(route.waypoints.enumerated()), id: \.offset) { index, waypoint in
                WaypointRow(
                    waypoint: waypoint,
                    index: index,
                    isLast: index == route.waypoints.count - 1,
                    themeManager: themeManager
                )
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial.opacity(themeManager.isDarkMode ? 0.2 : 0.8))
        )
    }
    
    // MARK: - 操作按钮
    private var actionButtonsView: some View {
        VStack(spacing: 12) {
            // 生成线路图按钮
            Button(action: {
                showFullScreenMap = true
            }) {
                HStack(spacing: 12) {
                    Image(systemName: "map.fill")
                        .font(.system(size: 16, weight: .semibold))
                    
                    Text("生成线路图")
                        .font(.system(size: 18, weight: .semibold))
                }
                .foregroundColor(.pulseAccent(isDarkMode: themeManager.isDarkMode))
                .frame(maxWidth: .infinity)
                .frame(height: 54)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.pulseAccent(isDarkMode: themeManager.isDarkMode), lineWidth: 2)
                        .background(
                            RoundedRectangle(cornerRadius: 16)
                                .fill(themeManager.isDarkMode ? Color.black : Color.white)
                        )
                )
            }
            .buttonStyle(.plain)
            
            // 开始骑行按钮
            Button(action: {
                // 直接进入地图并开始导航
                showFullScreenMap = true
            }) {
                HStack(spacing: 12) {
                    Image(systemName: "play.fill")
                        .font(.system(size: 16, weight: .semibold))
                    
                    Text("开始骑行")
                        .font(.system(size: 18, weight: .semibold))
                }
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .frame(height: 54)
                .background(
                    LinearGradient(
                        colors: [
                            Color.pulseAccent(isDarkMode: themeManager.isDarkMode),
                            Color.pulseAccent(isDarkMode: themeManager.isDarkMode).opacity(0.8)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .clipShape(RoundedRectangle(cornerRadius: 16))
                .shadow(color: Color.pulseAccent(isDarkMode: themeManager.isDarkMode).opacity(0.3), radius: 12, x: 0, y: 6)
            }
            .buttonStyle(.plain)
        }
    }
    
    // MARK: - 地图快照生成
    private func generateMapSnapshot() {
        guard route.mapSnapshot == nil else {
            mapSnapshot = route.mapSnapshot
            isLoadingSnapshot = false
            return
        }
        
        Task {
            do {
                let snapshot = try await createMapSnapshot()
                await MainActor.run {
                    self.mapSnapshot = snapshot
                    self.isLoadingSnapshot = false
                }
            } catch {
                await MainActor.run {
                    self.isLoadingSnapshot = false
                    print("❌ 地图快照生成失败: \(error.localizedDescription)")
                }
            }
        }
    }
    
    private func createMapSnapshot() async throws -> UIImage {
        return try await withCheckedThrowingContinuation { continuation in
            let mapSnapshotOptions = MKMapSnapshotter.Options()
            
            // 设置地图区域
            if !route.gpxTrack.isEmpty {
                let coordinates = route.gpxTrack.map { CLLocationCoordinate2D(latitude: $0.latitude, longitude: $0.longitude) }
                let region = calculateMapRegion(for: coordinates)
                mapSnapshotOptions.region = region
            } else if let firstWaypoint = route.waypoints.first {
                let center = CLLocationCoordinate2D(latitude: firstWaypoint.latitude, longitude: firstWaypoint.longitude)
                mapSnapshotOptions.region = MKCoordinateRegion(center: center, latitudinalMeters: 5000, longitudinalMeters: 5000)
            }
            
            mapSnapshotOptions.size = CGSize(width: 400, height: 250)
            mapSnapshotOptions.scale = UIScreen.main.scale
            
            let snapshotter = MKMapSnapshotter(options: mapSnapshotOptions)
            snapshotter.start { snapshot, error in
                if let error = error {
                    continuation.resume(throwing: error)
                    return
                }
                
                guard let snapshot = snapshot else {
                    continuation.resume(throwing: NSError(domain: "MapSnapshot", code: 0, userInfo: [NSLocalizedDescriptionKey: "快照为空"]))
                    return
                }
                
                continuation.resume(returning: snapshot.image)
            }
        }
    }
    
    private func calculateMapRegion(for coordinates: [CLLocationCoordinate2D]) -> MKCoordinateRegion {
        guard !coordinates.isEmpty else {
            return MKCoordinateRegion(center: CLLocationCoordinate2D(latitude: 39.9042, longitude: 116.4074), latitudinalMeters: 5000, longitudinalMeters: 5000)
        }
        
        let latitudes = coordinates.map { $0.latitude }
        let longitudes = coordinates.map { $0.longitude }
        
        let minLat = latitudes.min()!
        let maxLat = latitudes.max()!
        let minLon = longitudes.min()!
        let maxLon = longitudes.max()!
        
        let center = CLLocationCoordinate2D(
            latitude: (minLat + maxLat) / 2,
            longitude: (minLon + maxLon) / 2
        )
        
        let span = MKCoordinateSpan(
            latitudeDelta: (maxLat - minLat) * 1.3,
            longitudeDelta: (maxLon - minLon) * 1.3
        )
        
        return MKCoordinateRegion(center: center, span: span)
    }
}

// MARK: - 支持组件

// 路线统计项组件
struct RouteStatItem: View {
    let icon: String
    let title: String
    let value: String
    let themeManager: ThemeManager

    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            HStack(spacing: 4) {
                Image(systemName: icon)
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(.pulseAccent(isDarkMode: themeManager.isDarkMode))

                Text(title)
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(themeManager.isDarkMode ? .white.opacity(0.7) : .secondary)
            }

            Text(value)
                .font(.system(size: 16, weight: .bold))
                .foregroundColor(themeManager.isDarkMode ? .white : .black)
        }
    }
}

// 途径点行组件
struct WaypointRow: View {
    let waypoint: Waypoint
    let index: Int
    let isLast: Bool
    let themeManager: ThemeManager
    
    var body: some View {
        HStack(spacing: 12) {
            // 序号圆圈
            ZStack {
                Circle()
                    .fill(Color.pulseAccent(isDarkMode: themeManager.isDarkMode))
                    .frame(width: 24, height: 24)
                
                Text("\(index + 1)")
                    .font(.system(size: 12, weight: .bold))
                    .foregroundColor(.white)
            }
            
            VStack(alignment: .leading, spacing: 4) {
                Text(waypoint.name)
                    .font(.system(size: 14, weight: .semibold))
                    .foregroundColor(themeManager.isDarkMode ? .white : .black)
                
                if !waypoint.description.isEmpty {
                    Text(waypoint.description)
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(themeManager.isDarkMode ? .white.opacity(0.7) : .secondary)
                        .lineLimit(2)
                }
            }
            
            Spacer()
        }
        .padding(.vertical, 4)
        
        if !isLast {
            HStack {
                Rectangle()
                    .fill(Color.pulseAccent(isDarkMode: themeManager.isDarkMode).opacity(0.3))
                    .frame(width: 2, height: 20)
                    .offset(x: 11)
                
                Spacer()
            }
        }
    }
}
