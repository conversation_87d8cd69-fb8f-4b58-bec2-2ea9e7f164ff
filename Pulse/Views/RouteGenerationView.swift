import SwiftUI

// MARK: - 生成步骤枚举
enum GenerationStep: Int, CaseIterable {
    case preparing = 0
    case apiCall = 1
    case processing = 2
    case geocoding = 3
    case routing = 4
    case snapshot = 5
    case completed = 6

    var title: String {
        switch self {
        case .preparing: return "准备生成"
        case .apiCall: return "AI智能分析"
        case .processing: return "解析推荐"
        case .geocoding: return "地理编码"
        case .routing: return "路线规划"
        case .snapshot: return "生成快照"
        case .completed: return "生成完成"
        }
    }

    var description: String {
        switch self {
        case .preparing: return "正在初始化路线生成系统..."
        case .apiCall: return "DeepSeek AI正在分析您的需求并生成路线推荐..."
        case .processing: return "正在解析AI推荐的路线信息..."
        case .geocoding: return "正在获取推荐地点的精确地理坐标..."
        case .routing: return "正在生成真实的骑行轨迹和导航信息..."
        case .snapshot: return "正在制作精美的路线地图快照..."
        case .completed: return "所有路线已生成完成，准备展示结果"
        }
    }

    var icon: String {
        switch self {
        case .preparing: return "gear.circle.fill"
        case .apiCall: return "brain.head.profile.fill"
        case .processing: return "cpu.fill"
        case .geocoding: return "location.circle.fill"
        case .routing: return "map.circle.fill"
        case .snapshot: return "camera.circle.fill"
        case .completed: return "checkmark.circle.fill"
        }
    }

    var color: Color {
        switch self {
        case .preparing: return .blue
        case .apiCall: return .purple
        case .processing: return .indigo
        case .geocoding: return .green
        case .routing: return .orange
        case .snapshot: return .pink
        case .completed: return .green
        }
    }

    var estimatedDuration: TimeInterval {
        switch self {
        case .preparing: return 1.0
        case .apiCall: return 30.0 // 可能需要较长时间
        case .processing: return 5.0
        case .geocoding: return 10.0
        case .routing: return 15.0
        case .snapshot: return 8.0
        case .completed: return 0.0
        }
    }
}

// MARK: - 路线生成进度管理器
class RouteGenerationProgressManager: ObservableObject {
    @Published var currentStep: GenerationStep = .preparing
    @Published var stepProgress: Double = 0.0
    @Published var stepDetails: [String] = []
    @Published var isCompleted = false
    @Published var hasError = false
    @Published var errorMessage = ""
    @Published var generatedRoutes: [ExploreActivity] = []

    func updateStep(_ step: GenerationStep, progress: Double = 0.0, detail: String? = nil) {
        DispatchQueue.main.async {
            self.currentStep = step
            self.stepProgress = progress
            if let detail = detail {
                self.stepDetails.append(detail)
            }
        }
    }

    func addDetail(_ detail: String) {
        DispatchQueue.main.async {
            self.stepDetails.append(detail)
        }
    }

    func completeWithSuccess(_ routes: [ExploreActivity]) {
        DispatchQueue.main.async {
            self.generatedRoutes = routes
            self.currentStep = .completed
            self.stepProgress = 1.0
            self.isCompleted = true
        }
    }

    func completeWithError(_ message: String) {
        DispatchQueue.main.async {
            self.hasError = true
            self.errorMessage = message
        }
    }

    func reset() {
        DispatchQueue.main.async {
            self.currentStep = .preparing
            self.stepProgress = 0.0
            self.stepDetails.removeAll()
            self.isCompleted = false
            self.hasError = false
            self.errorMessage = ""
            self.generatedRoutes.removeAll()
        }
    }
}

// MARK: - 路线生成视图
struct RouteGenerationView: View {
    @ObservedObject var exploreManager: ExploreManager
    @ObservedObject var themeManager: ThemeManager
    @Environment(\.dismiss) private var dismiss

    let startLocation: String
    let requirements: String
    let minDistance: Double
    let maxDistance: Double

    @StateObject private var progressManager = RouteGenerationProgressManager()
    
    var body: some View {
        ZStack {
            // 动态背景渐变
            backgroundGradient
                .ignoresSafeArea()

            VStack(spacing: 0) {
                // 拖拽指示器
                dragIndicator

                // 头部
                headerSection

                // 主要内容 - 使用ScrollView确保可以滚动
                ScrollView {
                    VStack(spacing: 20) {
                        if progressManager.hasError {
                            errorView
                        } else if progressManager.isCompleted {
                            completedView
                        } else {
                            modernProgressView
                        }
                    }
                    .padding(.horizontal, 20)
                    .padding(.bottom, 100) // 为底部按钮留出空间
                }

                // 底部按钮 - 固定在底部
                bottomButtons
                    .background(
                        // 添加渐变遮罩效果
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color.clear,
                                themeManager.isDarkMode ? Color.black.opacity(0.8) : Color(.systemGroupedBackground).opacity(0.8),
                                themeManager.isDarkMode ? Color.black : Color(.systemGroupedBackground)
                            ]),
                            startPoint: .top,
                            endPoint: .bottom
                        )
                        .frame(height: 80)
                        .offset(y: -20)
                    )
            }
        }
        .onAppear {
            startGeneration()
        }
    }

    // MARK: - 动态背景渐变
    private var backgroundGradient: some View {
        LinearGradient(
            gradient: Gradient(colors: [
                themeManager.isDarkMode ? Color.black : Color(.systemGroupedBackground),
                progressManager.currentStep.color.opacity(0.03),
                themeManager.isDarkMode ? Color.black.opacity(0.8) : Color(.systemGroupedBackground)
            ]),
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        .animation(.easeInOut(duration: 1.0), value: progressManager.currentStep)
    }
    
    // MARK: - 拖拽指示器
    private var dragIndicator: some View {
        RoundedRectangle(cornerRadius: 2.5)
            .fill(Color.gray.opacity(0.3))
            .frame(width: 36, height: 5)
            .padding(.top, 8)
            .padding(.bottom, 16)
    }
    
    // MARK: - 头部区域
    private var headerSection: some View {
        VStack(spacing: 8) {
            HStack {
                VStack(alignment: .leading, spacing: 2) {
                    Text("智能路线生成")
                        .font(.system(size: 20, weight: .bold))
                        .foregroundColor(themeManager.isDarkMode ? .white : .black)

                    Text("基于AI为您定制专属骑行路线")
                        .font(.system(size: 12))
                        .foregroundColor(.secondary)
                }

                Spacer()

                Button(action: {
                    dismiss()
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .font(.system(size: 20))
                        .foregroundColor(.secondary)
                }
            }

            // 生成参数信息 - 更紧凑的布局
            HStack(spacing: 16) {
                HStack(spacing: 6) {
                    Image(systemName: "location.fill")
                        .font(.system(size: 12))
                        .foregroundColor(.blue)
                    Text(startLocation)
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(themeManager.isDarkMode ? .white.opacity(0.9) : .black)
                        .lineLimit(1)
                }

                Spacer()

                HStack(spacing: 6) {
                    Image(systemName: "ruler")
                        .font(.system(size: 12))
                        .foregroundColor(.orange)
                    Text("\(Int(minDistance))-\(Int(maxDistance))km")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(themeManager.isDarkMode ? .white.opacity(0.9) : .black)
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(themeManager.isDarkMode ? Color.gray.opacity(0.1) : Color.white)
            )
        }
        .padding(.horizontal, 20)
        .padding(.top, 8)
        .padding(.bottom, 4)
    }
    
    // MARK: - 参数行
    private func parameterRow(icon: String, title: String, value: String) -> some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.pulseAccent(isDarkMode: themeManager.isDarkMode))
                .frame(width: 20)
            
            Text(title)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.secondary)
            
            Spacer()
            
            Text(value)
                .font(.system(size: 14))
                .foregroundColor(themeManager.isDarkMode ? .white : .black)
                .lineLimit(1)
        }
    }
    
    // MARK: - 现代化进度视图
    private var modernProgressView: some View {
        VStack(spacing: 32) {
            // 主要进度圆环
            mainProgressRing

            // 当前步骤信息
            currentStepInfo

            // 步骤时间线
            stepsTimeline

            // 实时详情
            if !progressManager.stepDetails.isEmpty {
                realTimeDetails
            }
        }
        .padding(.top, 20)
    }

    // MARK: - 主要进度圆环
    private var mainProgressRing: some View {
        ZStack {
            // 背景圆环
            Circle()
                .stroke(
                    progressManager.currentStep.color.opacity(0.1),
                    style: StrokeStyle(lineWidth: 12, lineCap: .round)
                )
                .frame(width: 160, height: 160)

            // 进度圆环
            Circle()
                .trim(from: 0, to: CGFloat(progressManager.currentStep.rawValue) / CGFloat(GenerationStep.allCases.count - 1))
                .stroke(
                    AngularGradient(
                        gradient: Gradient(colors: [
                            progressManager.currentStep.color.opacity(0.3),
                            progressManager.currentStep.color,
                            progressManager.currentStep.color.opacity(0.8)
                        ]),
                        center: .center,
                        startAngle: .degrees(-90),
                        endAngle: .degrees(270)
                    ),
                    style: StrokeStyle(lineWidth: 12, lineCap: .round)
                )
                .frame(width: 160, height: 160)
                .rotationEffect(.degrees(-90))
                .animation(.easeInOut(duration: 1.0), value: progressManager.currentStep)

            // 中心内容
            VStack(spacing: 8) {
                // 步骤图标
                ZStack {
                    Circle()
                        .fill(progressManager.currentStep.color.opacity(0.1))
                        .frame(width: 60, height: 60)

                    Image(systemName: progressManager.currentStep.icon)
                        .font(.system(size: 24, weight: .semibold))
                        .foregroundColor(progressManager.currentStep.color)
                }
                .scaleEffect(progressManager.stepProgress > 0 ? 1.1 : 1.0)
                .animation(.spring(response: 0.5, dampingFraction: 0.6), value: progressManager.stepProgress)

                // 进度百分比
                Text("\(Int((Double(progressManager.currentStep.rawValue) / Double(GenerationStep.allCases.count - 1)) * 100))%")
                    .font(.system(size: 16, weight: .bold))
                    .foregroundColor(progressManager.currentStep.color)
            }
        }
    }
    
    // MARK: - 当前步骤信息
    private var currentStepInfo: some View {
        VStack(spacing: 16) {
            // 步骤标题
            Text(progressManager.currentStep.title)
                .font(.system(size: 28, weight: .bold))
                .foregroundColor(themeManager.isDarkMode ? .white : .black)
                .multilineTextAlignment(.center)

            // 步骤描述
            Text(progressManager.currentStep.description)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .lineLimit(3)
                .padding(.horizontal, 20)

            // 动态加载指示器
            HStack(spacing: 8) {
                ForEach(0..<3, id: \.self) { index in
                    Circle()
                        .fill(progressManager.currentStep.color)
                        .frame(width: 8, height: 8)
                        .scaleEffect(loadingDotScale(for: index))
                        .animation(
                            Animation.easeInOut(duration: 0.6)
                                .repeatForever(autoreverses: true)
                                .delay(Double(index) * 0.2),
                            value: progressManager.currentStep
                        )
                }
            }
            .padding(.top, 8)
        }
        .padding(24)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 20)
                        .stroke(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    progressManager.currentStep.color.opacity(0.3),
                                    progressManager.currentStep.color.opacity(0.1)
                                ]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            lineWidth: 1
                        )
                )
                .shadow(color: progressManager.currentStep.color.opacity(0.1), radius: 20, x: 0, y: 10)
        )
    }

    // MARK: - 加载点缩放动画
    private func loadingDotScale(for index: Int) -> CGFloat {
        let baseScale: CGFloat = 0.5
        let maxScale: CGFloat = 1.2

        // 简单的动画逻辑，实际中可以根据时间来计算
        return baseScale + (maxScale - baseScale) * 0.5
    }
    
    // MARK: - 步骤时间线
    private var stepsTimeline: some View {
        VStack(spacing: 0) {
            ForEach(Array(GenerationStep.allCases.enumerated()), id: \.offset) { index, step in
                if step != .completed {
                    timelineStepView(step: step, index: index)

                    if index < GenerationStep.allCases.count - 2 {
                        timelineConnector(isActive: progressManager.currentStep.rawValue > step.rawValue)
                    }
                }
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.gray.opacity(0.1), lineWidth: 1)
                )
        )
    }

    // MARK: - 时间线步骤视图
    private func timelineStepView(step: GenerationStep, index: Int) -> some View {
        HStack(spacing: 16) {
            // 状态指示器
            ZStack {
                Circle()
                    .fill(timelineStepColor(for: step))
                    .frame(width: 32, height: 32)
                    .overlay(
                        Circle()
                            .stroke(Color.white, lineWidth: 2)
                    )

                if step.rawValue < progressManager.currentStep.rawValue {
                    Image(systemName: "checkmark")
                        .font(.system(size: 14, weight: .bold))
                        .foregroundColor(.white)
                } else if step == progressManager.currentStep {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(0.6)
                } else {
                    Image(systemName: step.icon)
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(.white)
                }
            }
            .scaleEffect(step == progressManager.currentStep ? 1.1 : 1.0)
            .animation(.spring(response: 0.5, dampingFraction: 0.6), value: progressManager.currentStep)

            VStack(alignment: .leading, spacing: 4) {
                Text(step.title)
                    .font(.system(size: 16, weight: step == progressManager.currentStep ? .bold : .medium))
                    .foregroundColor(timelineTextColor(for: step))

                if step == progressManager.currentStep {
                    Text("正在进行中...")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(step.color)
                } else if step.rawValue < progressManager.currentStep.rawValue {
                    Text("已完成")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(.green)
                } else {
                    Text("等待中")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(.secondary)
                }
            }

            Spacer()

            // 预计时间
            if step.estimatedDuration > 0 {
                Text("\(Int(step.estimatedDuration))s")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(.secondary)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        Capsule()
                            .fill(Color.gray.opacity(0.1))
                    )
            }
        }
        .padding(.vertical, 12)
    }

    // MARK: - 时间线连接器
    private func timelineConnector(isActive: Bool) -> some View {
        Rectangle()
            .fill(isActive ?
                LinearGradient(
                    gradient: Gradient(colors: [Color.green, Color.green.opacity(0.3)]),
                    startPoint: .top,
                    endPoint: .bottom
                ) :
                LinearGradient(
                    gradient: Gradient(colors: [Color.gray.opacity(0.3), Color.gray.opacity(0.1)]),
                    startPoint: .top,
                    endPoint: .bottom
                )
            )
            .frame(width: 2, height: 20)
            .offset(x: 15)
            .animation(.easeInOut(duration: 0.5), value: isActive)
    }
    

    
    // MARK: - 实时详情视图
    private var realTimeDetails: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "info.circle.fill")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.blue)

                Text("实时进度")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(themeManager.isDarkMode ? .white : .black)

                Spacer()

                Text("\(progressManager.stepDetails.count) 条记录")
                    .font(.system(size: 12))
                    .foregroundColor(.secondary)
            }

            ScrollView(.vertical, showsIndicators: true) {
                LazyVStack(alignment: .leading, spacing: 8) {
                    ForEach(Array(progressManager.stepDetails.enumerated().reversed()), id: \.offset) { index, detail in
                        HStack(alignment: .top, spacing: 12) {
                            // 时间戳指示器
                            Circle()
                                .fill(Color.blue)
                                .frame(width: 6, height: 6)
                                .padding(.top, 6)

                            VStack(alignment: .leading, spacing: 2) {
                                Text(detail)
                                    .font(.system(size: 13, weight: .medium))
                                    .foregroundColor(themeManager.isDarkMode ? .white.opacity(0.9) : .primary)
                                    .fixedSize(horizontal: false, vertical: true)

                                Text("刚刚")
                                    .font(.system(size: 11))
                                    .foregroundColor(.secondary)
                            }

                            Spacer(minLength: 0)
                        }
                        .padding(.vertical, 4)
                        .padding(.horizontal, 4)
                    }
                }
                .padding(.vertical, 4)
            }
            .frame(minHeight: 100, maxHeight: 150)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(themeManager.isDarkMode ? Color.black.opacity(0.2) : Color.gray.opacity(0.05))
            )
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(themeManager.isDarkMode ? Color.gray.opacity(0.1) : Color.white)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.blue.opacity(0.2), lineWidth: 1)
                )
        )
    }

    // MARK: - 辅助方法
    private func timelineStepColor(for step: GenerationStep) -> Color {
        if step.rawValue < progressManager.currentStep.rawValue {
            return .green
        } else if step == progressManager.currentStep {
            return step.color
        } else {
            return .gray.opacity(0.3)
        }
    }

    private func timelineTextColor(for step: GenerationStep) -> Color {
        if step.rawValue <= progressManager.currentStep.rawValue {
            return themeManager.isDarkMode ? .white : .black
        } else {
            return .secondary
        }
    }



    // MARK: - 完成视图
    private var completedView: some View {
        VStack(spacing: 32) {
            // 成功动画图标
            ZStack {
                // 外圈动画
                Circle()
                    .stroke(Color.green.opacity(0.2), lineWidth: 4)
                    .frame(width: 120, height: 120)
                    .scaleEffect(1.2)
                    .opacity(0.6)
                    .animation(
                        Animation.easeInOut(duration: 2.0).repeatForever(autoreverses: true),
                        value: progressManager.isCompleted
                    )

                // 中圈
                Circle()
                    .fill(
                        RadialGradient(
                            gradient: Gradient(colors: [
                                Color.green.opacity(0.1),
                                Color.green.opacity(0.05)
                            ]),
                            center: .center,
                            startRadius: 20,
                            endRadius: 60
                        )
                    )
                    .frame(width: 100, height: 100)

                // 成功图标
                Image(systemName: "checkmark.circle.fill")
                    .font(.system(size: 50, weight: .bold))
                    .foregroundColor(.green)
                    .scaleEffect(progressManager.isCompleted ? 1.0 : 0.5)
                    .animation(.spring(response: 0.6, dampingFraction: 0.6), value: progressManager.isCompleted)
            }

            VStack(spacing: 16) {
                Text("🎉 路线生成完成！")
                    .font(.system(size: 28, weight: .bold))
                    .foregroundColor(themeManager.isDarkMode ? .white : .black)
                    .multilineTextAlignment(.center)

                Text("已为您生成 \(progressManager.generatedRoutes.count) 条精选骑行路线\n每条路线都经过AI智能优化，包含详细的导航信息")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .lineSpacing(4)
            }

            // 路线预览卡片
            if !progressManager.generatedRoutes.isEmpty {
                modernRoutePreviewCards
            }
        }
        .padding(.top, 20)
    }

    // MARK: - 现代路线预览卡片
    private var modernRoutePreviewCards: some View {
        VStack(spacing: 16) {
            ForEach(Array(progressManager.generatedRoutes.prefix(3).enumerated()), id: \.offset) { index, route in
                Button(action: {
                    // 点击卡片查看路线详情
                    showRouteDetail(route)
                }) {
                    HStack(spacing: 16) {
                        // 路线编号和图标
                        ZStack {
                            RoundedRectangle(cornerRadius: 12)
                                .fill(
                                    LinearGradient(
                                        gradient: Gradient(colors: [
                                            Color.pulseAccent(isDarkMode: themeManager.isDarkMode),
                                            Color.pulseAccent(isDarkMode: themeManager.isDarkMode).opacity(0.7)
                                        ]),
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    )
                                )
                                .frame(width: 50, height: 50)

                            VStack(spacing: 2) {
                                Text("\(index + 1)")
                                    .font(.system(size: 16, weight: .bold))
                                    .foregroundColor(.white)

                                Image(systemName: "map.fill")
                                    .font(.system(size: 12))
                                    .foregroundColor(.white.opacity(0.8))
                            }
                        }

                        VStack(alignment: .leading, spacing: 6) {
                            Text(route.title)
                                .font(.system(size: 16, weight: .bold))
                                .foregroundColor(themeManager.isDarkMode ? .white : .black)
                                .lineLimit(2)
                                .multilineTextAlignment(.leading)

                            HStack(spacing: 16) {
                                HStack(spacing: 4) {
                                    Image(systemName: "ruler")
                                        .font(.system(size: 12))
                                        .foregroundColor(.pulseAccent(isDarkMode: themeManager.isDarkMode))
                                    Text("\(String(format: "%.1f", route.distance))km")
                                        .font(.system(size: 13, weight: .medium))
                                        .foregroundColor(.secondary)
                                }

                                HStack(spacing: 4) {
                                    Image(systemName: "clock")
                                        .font(.system(size: 12))
                                        .foregroundColor(.pulseAccent(isDarkMode: themeManager.isDarkMode))
                                    Text(route.duration)
                                        .font(.system(size: 13, weight: .medium))
                                        .foregroundColor(.secondary)
                                }
                            }
                        }

                        Spacer()

                        VStack(spacing: 4) {
                            Image(systemName: "chevron.right.circle.fill")
                                .font(.system(size: 20))
                                .foregroundColor(.pulseAccent(isDarkMode: themeManager.isDarkMode))

                            Text("查看详情")
                                .font(.system(size: 10, weight: .medium))
                                .foregroundColor(.secondary)
                        }
                    }
                    .padding(16)
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(.ultraThinMaterial)
                            .overlay(
                                RoundedRectangle(cornerRadius: 16)
                                    .stroke(
                                        LinearGradient(
                                            gradient: Gradient(colors: [
                                                Color.pulseAccent(isDarkMode: themeManager.isDarkMode).opacity(0.3),
                                                Color.pulseAccent(isDarkMode: themeManager.isDarkMode).opacity(0.1)
                                            ]),
                                            startPoint: .topLeading,
                                            endPoint: .bottomTrailing
                                        ),
                                        lineWidth: 1
                                    )
                            )
                            .shadow(color: .black.opacity(0.05), radius: 10, x: 0, y: 4)
                    )
                }
                .buttonStyle(.plain)
            }
        }
    }

    // MARK: - 显示路线详情
    private func showRouteDetail(_ route: ExploreActivity) {
        // 确保路线已添加到ExploreManager
        if !exploreManager.exploreActivities.contains(where: { $0.id == route.id }) {
            exploreManager.exploreActivities.append(route)
        }

        // 关闭当前界面，用户可以在主界面查看路线详情
        dismiss()
    }

    // MARK: - 错误视图
    private var errorView: some View {
        VStack(spacing: 32) {
            // 错误动画图标
            ZStack {
                // 外圈警告动画
                Circle()
                    .stroke(Color.red.opacity(0.2), lineWidth: 4)
                    .frame(width: 120, height: 120)
                    .scaleEffect(1.1)
                    .opacity(0.7)
                    .animation(
                        Animation.easeInOut(duration: 1.5).repeatForever(autoreverses: true),
                        value: progressManager.hasError
                    )

                // 背景圆
                Circle()
                    .fill(
                        RadialGradient(
                            gradient: Gradient(colors: [
                                Color.red.opacity(0.1),
                                Color.red.opacity(0.05)
                            ]),
                            center: .center,
                            startRadius: 20,
                            endRadius: 60
                        )
                    )
                    .frame(width: 100, height: 100)

                // 错误图标
                Image(systemName: "exclamationmark.triangle.fill")
                    .font(.system(size: 50, weight: .bold))
                    .foregroundColor(.red)
                    .scaleEffect(progressManager.hasError ? 1.0 : 0.5)
                    .animation(.spring(response: 0.6, dampingFraction: 0.6), value: progressManager.hasError)
            }

            VStack(spacing: 16) {
                Text("⚠️ 生成失败")
                    .font(.system(size: 28, weight: .bold))
                    .foregroundColor(themeManager.isDarkMode ? .white : .black)

                Text(progressManager.errorMessage.isEmpty ?
                    "路线生成过程中出现了问题\n请检查网络连接后重试" :
                    progressManager.errorMessage)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .lineSpacing(4)
                    .padding(.horizontal, 20)
            }

            // 错误详情（如果有的话）
            if !progressManager.stepDetails.isEmpty {
                VStack(alignment: .leading, spacing: 8) {
                    Text("错误详情:")
                        .font(.system(size: 14, weight: .semibold))
                        .foregroundColor(.red)

                    ForEach(progressManager.stepDetails.suffix(3), id: \.self) { detail in
                        Text("• \(detail)")
                            .font(.system(size: 12))
                            .foregroundColor(.secondary)
                    }
                }
                .padding(16)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.red.opacity(0.05))
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(Color.red.opacity(0.2), lineWidth: 1)
                        )
                )
            }
        }
        .padding(.top, 20)
    }

    // MARK: - 底部按钮
    private var bottomButtons: some View {
        VStack(spacing: 12) {
            if progressManager.isCompleted {
                Button(action: {
                    // 将生成的路线添加到ExploreManager并保持在当前界面
                    for route in progressManager.generatedRoutes {
                        if !exploreManager.exploreActivities.contains(where: { $0.id == route.id }) {
                            exploreManager.exploreActivities.append(route)
                        }
                    }
                    // 不要立即dismiss，让用户可以查看路线详情
                }) {
                    HStack(spacing: 12) {
                        Image(systemName: "checkmark.circle.fill")
                            .font(.system(size: 18))

                        Text("路线已保存")
                            .font(.system(size: 18, weight: .semibold))
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .frame(height: 56)
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(
                                LinearGradient(
                                    gradient: Gradient(colors: [
                                        Color.green,
                                        Color.green.opacity(0.8)
                                    ]),
                                    startPoint: .leading,
                                    endPoint: .trailing
                                )
                            )
                            .shadow(color: Color.green.opacity(0.3), radius: 12, x: 0, y: 6)
                    )
                }
                .buttonStyle(.plain)

                // 添加关闭按钮
                Button(action: {
                    dismiss()
                }) {
                    Text("关闭")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.secondary)
                        .frame(maxWidth: .infinity)
                        .frame(height: 48)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(Color.secondary.opacity(0.3), lineWidth: 1)
                        )
                }
                .buttonStyle(.plain)

            } else if progressManager.hasError {
                HStack(spacing: 12) {
                    Button(action: {
                        retryGeneration()
                    }) {
                        HStack(spacing: 8) {
                            Image(systemName: "arrow.clockwise")
                                .font(.system(size: 16))

                            Text("重新生成")
                                .font(.system(size: 16, weight: .semibold))
                        }
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .frame(height: 56)
                        .background(
                            RoundedRectangle(cornerRadius: 16)
                                .fill(Color.red)
                                .shadow(color: Color.red.opacity(0.3), radius: 12, x: 0, y: 6)
                        )
                    }
                    .buttonStyle(.plain)

                    Button(action: {
                        dismiss()
                    }) {
                        Text("取消")
                            .font(.system(size: 16, weight: .semibold))
                            .foregroundColor(.secondary)
                            .frame(maxWidth: .infinity)
                            .frame(height: 56)
                            .background(
                                RoundedRectangle(cornerRadius: 16)
                                    .stroke(Color.secondary.opacity(0.3), lineWidth: 1)
                            )
                    }
                    .buttonStyle(.plain)
                }

            } else {
                // 生成中的取消按钮
                Button(action: {
                    dismiss()
                }) {
                    Text("取消生成")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.secondary)
                        .frame(maxWidth: .infinity)
                        .frame(height: 48)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(Color.secondary.opacity(0.3), lineWidth: 1)
                        )
                }
                .buttonStyle(.plain)
            }
        }
        .padding(.bottom, 20)
    }

    // MARK: - 生成逻辑
    private func startGeneration() {
        print("🚀 开始路线生成流程")

        // 重置状态
        progressManager.reset()

        // 开始生成流程
        Task {
            await performGeneration()
        }
    }

    private func performGeneration() async {
        do {
            // 步骤1: 准备
            progressManager.updateStep(.preparing, progress: 0.1, detail: "初始化路线生成系统...")
            try await Task.sleep(nanoseconds: 1_000_000_000) // 1秒

            progressManager.addDetail("检查网络连接状态")
            try await Task.sleep(nanoseconds: 500_000_000)

            // 步骤2: AI调用
            progressManager.updateStep(.apiCall, progress: 0.2, detail: "正在连接DeepSeek AI服务...")
            try await Task.sleep(nanoseconds: 500_000_000)

            progressManager.addDetail("发送路线生成请求到AI服务器")

            // 实际调用生成
            await MainActor.run {
                exploreManager.generateCustomRoutes(
                    startLocation: startLocation,
                    requirements: requirements,
                    minDistance: minDistance,
                    maxDistance: maxDistance
                )
            }

            // 监控生成进度
            await monitorGeneration()

        } catch {
            progressManager.completeWithError(error.localizedDescription)
        }
    }

    private func monitorGeneration() async {
        var checkCount = 0
        let maxChecks = 360 // 最多检查360次（3分钟，每0.5秒检查一次）
        var lastStepTime = Date()

        while checkCount < maxChecks {
            do {
                try await Task.sleep(nanoseconds: 500_000_000) // 0.5秒检查一次
            } catch {
                break
            }
            checkCount += 1

            await MainActor.run {
                // 检查错误
                if exploreManager.error != nil {
                    Task {
                        progressManager.completeWithError(exploreManager.error ?? "生成过程中出现未知错误")
                    }
                    return
                }

                // 检查完成
                if !exploreManager.isLoading && !exploreManager.exploreActivities.isEmpty {
                    Task {
                        await completeGeneration()
                    }
                    return
                }

                // 根据时间更新步骤
                let currentTime = Date()
                let _ = currentTime.timeIntervalSince(lastStepTime)

                if checkCount <= 10 {
                    // AI调用阶段 (前5秒)
                    if progressManager.currentStep != .apiCall {
                        progressManager.updateStep(.apiCall, progress: 0.3, detail: "AI正在分析您的需求...")
                    }
                    if checkCount == 5 {
                        progressManager.addDetail("DeepSeek正在生成路线推荐...")
                    }
                } else if checkCount <= 30 {
                    // 处理阶段 (5-15秒)
                    if progressManager.currentStep != .processing {
                        progressManager.updateStep(.processing, progress: 0.4, detail: "正在解析AI推荐结果...")
                        lastStepTime = currentTime
                    }
                    if checkCount == 20 {
                        progressManager.addDetail("提取推荐地点和路线信息...")
                    }
                } else if checkCount <= 80 {
                    // 地理编码阶段 (15-40秒)
                    if progressManager.currentStep != .geocoding {
                        progressManager.updateStep(.geocoding, progress: 0.5, detail: "正在获取地理坐标...")
                        lastStepTime = currentTime
                    }
                    if checkCount == 50 {
                        progressManager.addDetail("解析推荐地点的精确位置...")
                    }
                    if checkCount == 70 {
                        progressManager.addDetail("验证地理坐标的准确性...")
                    }
                } else if checkCount <= 200 {
                    // 路线规划阶段 (40-100秒)
                    if progressManager.currentStep != .routing {
                        progressManager.updateStep(.routing, progress: 0.7, detail: "正在生成骑行路线...")
                        lastStepTime = currentTime
                    }
                    if checkCount == 120 {
                        progressManager.addDetail("计算最优骑行路径...")
                    }
                    if checkCount == 160 {
                        progressManager.addDetail("生成导航指令和路线详情...")
                    }
                } else {
                    // 快照生成阶段 (100秒以后)
                    if progressManager.currentStep != .snapshot {
                        progressManager.updateStep(.snapshot, progress: 0.9, detail: "正在制作路线地图快照...")
                        lastStepTime = currentTime
                    }
                    if checkCount == 250 {
                        progressManager.addDetail("渲染精美的路线预览图...")
                    }
                    if checkCount == 300 {
                        progressManager.addDetail("完善路线统计信息...")
                    }
                }
            }
        }

        // 超时处理
        progressManager.completeWithError("生成超时，请检查网络连接后重试")
    }

    @MainActor
    private func completeGeneration() async {
        progressManager.updateStep(.snapshot, progress: 0.95, detail: "正在完成最后的处理...")

        // 短暂延迟以显示快照步骤
        try? await Task.sleep(nanoseconds: 1_000_000_000)

        // 将ExploreManager中的路线复制到progressManager
        let routes = exploreManager.exploreActivities
        progressManager.completeWithSuccess(routes)

        progressManager.addDetail("成功生成 \(routes.count) 条精选路线")
        print("✅ 路线生成完成: \(routes.count) 条路线")
    }



    private func retryGeneration() {
        progressManager.reset()
        startGeneration()
    }
}


