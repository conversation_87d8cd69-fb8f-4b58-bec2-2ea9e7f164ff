import SwiftUI

// MARK: - 路线生成视图
struct RouteGenerationView: View {
    @ObservedObject var exploreManager: ExploreManager
    @ObservedObject var themeManager: ThemeManager
    @Environment(\.dismiss) private var dismiss
    
    let startLocation: String
    let requirements: String
    let minDistance: Double
    let maxDistance: Double
    
    @State private var currentStep: GenerationStep = .preparing
    @State private var stepProgress: Double = 0.0
    @State private var stepDetails: [String] = []
    @State private var isCompleted = false
    @State private var hasError = false
    @State private var errorMessage = ""
    
    // 生成步骤枚举
    enum GenerationStep: CaseIterable {
        case preparing
        case analyzing
        case requesting
        case processing
        case geocoding
        case routing
        case finalizing
        case completed
        
        var title: String {
            switch self {
            case .preparing: return "准备生成"
            case .analyzing: return "分析需求"
            case .requesting: return "请求AI推荐"
            case .processing: return "处理推荐结果"
            case .geocoding: return "获取地理坐标"
            case .routing: return "生成骑行路线"
            case .finalizing: return "完善路线信息"
            case .completed: return "生成完成"
            }
        }
        
        var description: String {
            switch self {
            case .preparing: return "正在初始化路线生成系统..."
            case .analyzing: return "分析您的位置和骑行需求..."
            case .requesting: return "向DeepSeek AI请求路线推荐..."
            case .processing: return "解析AI推荐的路线信息..."
            case .geocoding: return "获取推荐地点的精确坐标..."
            case .routing: return "生成真实的骑行轨迹..."
            case .finalizing: return "完善路线详情和统计信息..."
            case .completed: return "路线生成完成，准备展示结果"
            }
        }
        
        var icon: String {
            switch self {
            case .preparing: return "gear"
            case .analyzing: return "magnifyingglass"
            case .requesting: return "brain.head.profile"
            case .processing: return "cpu"
            case .geocoding: return "location"
            case .routing: return "map"
            case .finalizing: return "checkmark.circle"
            case .completed: return "checkmark.circle.fill"
            }
        }
        
        var color: Color {
            switch self {
            case .preparing: return .blue
            case .analyzing: return .orange
            case .requesting: return .purple
            case .processing: return .indigo
            case .geocoding: return .green
            case .routing: return .red
            case .finalizing: return .mint
            case .completed: return .green
            }
        }
    }
    
    var body: some View {
        ZStack {
            // 背景
            (themeManager.isDarkMode ? Color.black : Color(.systemGroupedBackground))
                .ignoresSafeArea()
            
            VStack(spacing: 0) {
                // 拖拽指示器
                dragIndicator
                
                // 头部
                headerSection
                
                // 主要内容
                if hasError {
                    errorView
                } else if isCompleted {
                    completedView
                } else {
                    generationProgressView
                }
                
                Spacer()
                
                // 底部按钮
                bottomButtons
            }
            .padding(.horizontal, 20)
        }
        .onAppear {
            startGeneration()
        }
    }
    
    // MARK: - 拖拽指示器
    private var dragIndicator: some View {
        RoundedRectangle(cornerRadius: 2.5)
            .fill(Color.gray.opacity(0.3))
            .frame(width: 36, height: 5)
            .padding(.top, 8)
            .padding(.bottom, 16)
    }
    
    // MARK: - 头部区域
    private var headerSection: some View {
        VStack(spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("智能路线生成")
                        .font(.system(size: 24, weight: .bold))
                        .foregroundColor(themeManager.isDarkMode ? .white : .black)
                    
                    Text("基于AI为您定制专属骑行路线")
                        .font(.system(size: 14))
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Button(action: {
                    dismiss()
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .font(.system(size: 24))
                        .foregroundColor(.secondary)
                }
            }
            
            // 生成参数信息
            VStack(spacing: 8) {
                parameterRow(icon: "location.fill", title: "起始位置", value: startLocation)
                parameterRow(icon: "ruler", title: "距离范围", value: "\(Int(minDistance))-\(Int(maxDistance))公里")
                if !requirements.isEmpty {
                    parameterRow(icon: "text.bubble", title: "特殊需求", value: requirements)
                }
            }
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(themeManager.isDarkMode ? Color.gray.opacity(0.1) : Color.white)
            )
        }
    }
    
    // MARK: - 参数行
    private func parameterRow(icon: String, title: String, value: String) -> some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.pulseAccent(isDarkMode: themeManager.isDarkMode))
                .frame(width: 20)
            
            Text(title)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.secondary)
            
            Spacer()
            
            Text(value)
                .font(.system(size: 14))
                .foregroundColor(themeManager.isDarkMode ? .white : .black)
                .lineLimit(1)
        }
    }
    
    // MARK: - 生成进度视图
    private var generationProgressView: some View {
        VStack(spacing: 24) {
            // 当前步骤大卡片
            currentStepCard
            
            // 步骤列表
            stepsListView
            
            // 详细信息
            if !stepDetails.isEmpty {
                detailsView
            }
        }
        .padding(.top, 20)
    }
    
    // MARK: - 当前步骤卡片
    private var currentStepCard: some View {
        VStack(spacing: 16) {
            // 图标和进度环
            ZStack {
                Circle()
                    .stroke(currentStep.color.opacity(0.2), lineWidth: 8)
                    .frame(width: 80, height: 80)
                
                Circle()
                    .trim(from: 0, to: stepProgress)
                    .stroke(currentStep.color, style: StrokeStyle(lineWidth: 8, lineCap: .round))
                    .frame(width: 80, height: 80)
                    .rotationEffect(.degrees(-90))
                    .animation(.easeInOut(duration: 0.5), value: stepProgress)
                
                Image(systemName: currentStep.icon)
                    .font(.system(size: 24, weight: .semibold))
                    .foregroundColor(currentStep.color)
            }
            
            VStack(spacing: 8) {
                Text(currentStep.title)
                    .font(.system(size: 20, weight: .bold))
                    .foregroundColor(themeManager.isDarkMode ? .white : .black)
                
                Text(currentStep.description)
                    .font(.system(size: 14))
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .lineLimit(2)
            }
        }
        .padding(24)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(themeManager.isDarkMode ? Color.gray.opacity(0.1) : Color.white)
                .shadow(color: .black.opacity(0.05), radius: 10, x: 0, y: 2)
        )
    }
    
    // MARK: - 步骤列表
    private var stepsListView: some View {
        VStack(spacing: 0) {
            ForEach(Array(GenerationStep.allCases.enumerated()), id: \.offset) { index, step in
                if step != .completed {
                    stepRowView(step: step, index: index)
                    
                    if index < GenerationStep.allCases.count - 2 {
                        stepConnector(isActive: currentStep.rawValue > step.rawValue)
                    }
                }
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(themeManager.isDarkMode ? Color.gray.opacity(0.1) : Color.white)
        )
    }
    
    // MARK: - 步骤行视图
    private func stepRowView(step: GenerationStep, index: Int) -> some View {
        HStack(spacing: 12) {
            // 状态指示器
            ZStack {
                Circle()
                    .fill(stepStatusColor(for: step))
                    .frame(width: 24, height: 24)
                
                if step.rawValue < currentStep.rawValue {
                    Image(systemName: "checkmark")
                        .font(.system(size: 12, weight: .bold))
                        .foregroundColor(.white)
                } else if step == currentStep {
                    Circle()
                        .fill(Color.white)
                        .frame(width: 8, height: 8)
                } else {
                    Text("\(index + 1)")
                        .font(.system(size: 10, weight: .bold))
                        .foregroundColor(.white)
                }
            }
            
            VStack(alignment: .leading, spacing: 2) {
                Text(step.title)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(stepTextColor(for: step))
                
                if step == currentStep {
                    Text(step.description)
                        .font(.system(size: 12))
                        .foregroundColor(.secondary)
                        .lineLimit(1)
                }
            }
            
            Spacer()
            
            if step == currentStep {
                ProgressView()
                    .scaleEffect(0.8)
            }
        }
        .padding(.vertical, 8)
    }
    
    // MARK: - 步骤连接器
    private func stepConnector(isActive: Bool) -> some View {
        Rectangle()
            .fill(isActive ? Color.green : Color.gray.opacity(0.3))
            .frame(width: 2, height: 16)
            .offset(x: 11)
    }
    
    // MARK: - 辅助方法
    private func stepStatusColor(for step: GenerationStep) -> Color {
        if step.rawValue < currentStep.rawValue {
            return .green
        } else if step == currentStep {
            return step.color
        } else {
            return .gray.opacity(0.3)
        }
    }
    
    private func stepTextColor(for step: GenerationStep) -> Color {
        if step.rawValue <= currentStep.rawValue {
            return themeManager.isDarkMode ? .white : .black
        } else {
            return .secondary
        }
    }

    // MARK: - 详细信息视图
    private var detailsView: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("详细进度")
                .font(.system(size: 16, weight: .semibold))
                .foregroundColor(themeManager.isDarkMode ? .white : .black)

            ForEach(stepDetails.indices, id: \.self) { index in
                HStack(spacing: 8) {
                    Circle()
                        .fill(Color.green)
                        .frame(width: 6, height: 6)

                    Text(stepDetails[index])
                        .font(.system(size: 12))
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(themeManager.isDarkMode ? Color.gray.opacity(0.1) : Color.white)
        )
    }

    // MARK: - 完成视图
    private var completedView: some View {
        VStack(spacing: 24) {
            // 成功图标
            ZStack {
                Circle()
                    .fill(Color.green.opacity(0.1))
                    .frame(width: 100, height: 100)

                Image(systemName: "checkmark.circle.fill")
                    .font(.system(size: 50))
                    .foregroundColor(.green)
            }

            VStack(spacing: 12) {
                Text("路线生成完成！")
                    .font(.system(size: 24, weight: .bold))
                    .foregroundColor(themeManager.isDarkMode ? .white : .black)

                Text("已为您生成 \(exploreManager.exploreActivities.count) 条精选骑行路线")
                    .font(.system(size: 16))
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }

            // 路线预览卡片
            if !exploreManager.exploreActivities.isEmpty {
                routePreviewCards
            }
        }
        .padding(.top, 40)
    }

    // MARK: - 路线预览卡片
    private var routePreviewCards: some View {
        VStack(spacing: 12) {
            ForEach(exploreManager.exploreActivities.prefix(2), id: \.id) { route in
                HStack(spacing: 12) {
                    // 路线图标
                    ZStack {
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color.pulseAccent(isDarkMode: themeManager.isDarkMode).opacity(0.1))
                            .frame(width: 40, height: 40)

                        Image(systemName: "map.fill")
                            .font(.system(size: 16))
                            .foregroundColor(.pulseAccent(isDarkMode: themeManager.isDarkMode))
                    }

                    VStack(alignment: .leading, spacing: 4) {
                        Text(route.title)
                            .font(.system(size: 14, weight: .semibold))
                            .foregroundColor(themeManager.isDarkMode ? .white : .black)
                            .lineLimit(1)

                        HStack(spacing: 12) {
                            Label("\(String(format: "%.1f", route.distance))km", systemImage: "ruler")
                            Label(route.duration, systemImage: "clock")
                        }
                        .font(.system(size: 12))
                        .foregroundColor(.secondary)
                    }

                    Spacer()

                    Image(systemName: "chevron.right")
                        .font(.system(size: 12))
                        .foregroundColor(.secondary)
                }
                .padding(12)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(themeManager.isDarkMode ? Color.gray.opacity(0.1) : Color.white)
                )
            }
        }
    }

    // MARK: - 错误视图
    private var errorView: some View {
        VStack(spacing: 24) {
            // 错误图标
            ZStack {
                Circle()
                    .fill(Color.red.opacity(0.1))
                    .frame(width: 100, height: 100)

                Image(systemName: "exclamationmark.triangle.fill")
                    .font(.system(size: 50))
                    .foregroundColor(.red)
            }

            VStack(spacing: 12) {
                Text("生成失败")
                    .font(.system(size: 24, weight: .bold))
                    .foregroundColor(themeManager.isDarkMode ? .white : .black)

                Text(errorMessage.isEmpty ? "路线生成过程中出现了问题，请稍后重试" : errorMessage)
                    .font(.system(size: 16))
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
        }
        .padding(.top, 40)
    }

    // MARK: - 底部按钮
    private var bottomButtons: some View {
        VStack(spacing: 12) {
            if isCompleted {
                Button(action: {
                    dismiss()
                }) {
                    Text("查看路线")
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .frame(height: 54)
                        .background(
                            RoundedRectangle(cornerRadius: 16)
                                .fill(Color.pulseAccent(isDarkMode: themeManager.isDarkMode))
                        )
                }
            } else if hasError {
                Button(action: {
                    retryGeneration()
                }) {
                    Text("重新生成")
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .frame(height: 54)
                        .background(
                            RoundedRectangle(cornerRadius: 16)
                                .fill(Color.pulseAccent(isDarkMode: themeManager.isDarkMode))
                        )
                }
            } else {
                Button(action: {
                    dismiss()
                }) {
                    Text("取消生成")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.secondary)
                        .frame(maxWidth: .infinity)
                        .frame(height: 44)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(Color.secondary.opacity(0.3), lineWidth: 1)
                        )
                }
            }
        }
        .padding(.bottom, 20)
    }

    // MARK: - 生成逻辑
    private func startGeneration() {
        print("🚀 开始路线生成流程")

        // 重置状态
        currentStep = .preparing
        stepProgress = 0.0
        stepDetails = []
        hasError = false
        isCompleted = false

        // 开始生成流程
        Task {
            await performGeneration()
        }
    }

    private func performGeneration() async {
        do {
            // 步骤1: 准备
            await updateStep(.preparing, progress: 0.2, detail: "初始化生成系统")
            try await Task.sleep(nanoseconds: 500_000_000) // 0.5秒

            // 步骤2: 分析
            await updateStep(.analyzing, progress: 0.3, detail: "分析位置: \(startLocation)")
            try await Task.sleep(nanoseconds: 800_000_000) // 0.8秒
            await addDetail("分析骑行需求和偏好")
            try await Task.sleep(nanoseconds: 500_000_000)

            // 步骤3: 请求AI
            await updateStep(.requesting, progress: 0.4, detail: "连接DeepSeek AI服务")
            try await Task.sleep(nanoseconds: 600_000_000)
            await addDetail("发送路线生成请求")

            // 实际调用生成
            await MainActor.run {
                exploreManager.generateCustomRoutes(
                    startLocation: startLocation,
                    requirements: requirements,
                    minDistance: minDistance,
                    maxDistance: maxDistance
                )
            }

            // 监控生成进度
            await monitorGeneration()

        } catch {
            await handleError(error.localizedDescription)
        }
    }

    private func monitorGeneration() async {
        var checkCount = 0
        let maxChecks = 60 // 最多检查60次（30秒）

        while checkCount < maxChecks {
            do {
                try await Task.sleep(nanoseconds: 500_000_000) // 0.5秒检查一次
            } catch {
                break
            }
            checkCount += 1

            await MainActor.run {
                if exploreManager.error != nil {
                    Task {
                        await handleError(exploreManager.error ?? "生成过程中出现未知错误")
                    }
                    return
                }

                if !exploreManager.isLoading && !exploreManager.exploreActivities.isEmpty {
                    Task {
                        await completeGeneration()
                    }
                    return
                }

                // 更新进度
                let progress = min(0.4 + (Double(checkCount) / Double(maxChecks)) * 0.6, 1.0)

                if checkCount < 10 {
                    currentStep = .processing
                    stepProgress = progress
                } else if checkCount < 20 {
                    currentStep = .geocoding
                    stepProgress = progress
                } else if checkCount < 40 {
                    currentStep = .routing
                    stepProgress = progress
                } else {
                    currentStep = .finalizing
                    stepProgress = progress
                }
            }
        }

        // 超时处理
        await handleError("生成超时，请检查网络连接后重试")
    }

    @MainActor
    private func updateStep(_ step: GenerationStep, progress: Double, detail: String) async {
        currentStep = step
        stepProgress = progress
        stepDetails.append(detail)
    }

    @MainActor
    private func addDetail(_ detail: String) async {
        stepDetails.append(detail)
    }

    @MainActor
    private func completeGeneration() async {
        currentStep = .completed
        stepProgress = 1.0
        isCompleted = true
        stepDetails.append("成功生成 \(exploreManager.exploreActivities.count) 条路线")
    }

    @MainActor
    private func handleError(_ message: String) async {
        hasError = true
        errorMessage = message
        print("❌ 路线生成失败: \(message)")
    }

    private func retryGeneration() {
        hasError = false
        errorMessage = ""
        startGeneration()
    }
}

// MARK: - GenerationStep扩展
extension RouteGenerationView.GenerationStep: RawRepresentable {
    var rawValue: Int {
        switch self {
        case .preparing: return 0
        case .analyzing: return 1
        case .requesting: return 2
        case .processing: return 3
        case .geocoding: return 4
        case .routing: return 5
        case .finalizing: return 6
        case .completed: return 7
        }
    }

    init?(rawValue: Int) {
        switch rawValue {
        case 0: self = .preparing
        case 1: self = .analyzing
        case 2: self = .requesting
        case 3: self = .processing
        case 4: self = .geocoding
        case 5: self = .routing
        case 6: self = .finalizing
        case 7: self = .completed
        default: return nil
        }
    }
}
